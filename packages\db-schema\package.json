{"name": "@rie/db-schema", "version": "1.0.0", "type": "module", "description": "Database schema package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}, "./entity-types": {"types": "./build/dts/entity-types/index.d.ts", "import": "./build/entity-types/index.js", "require": "./build/entity-types/index.js"}, "./schemas": {"types": "./build/dts/schemas/index.d.ts", "import": "./build/schemas/index.js", "require": "./build/schemas/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write", "db:generate": "drizzle-kit generate --config drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config drizzle.config.ts", "db:push": "drizzle-kit push --config drizzle.config.ts", "db:studio": "drizzle-kit studio --config drizzle.config.ts", "db:start": "./start-db.sh"}, "dependencies": {"@rie/biome-config": "workspace:*", "@rie/utils": "workspace:*", "@t3-oss/env-core": "^0.13.4"}, "peerDependencies": {"drizzle-orm": "^0.43.1", "effect": "^3.15.0"}, "devDependencies": {"@types/node": "^22.15.17", "drizzle-kit": "^0.31.1", "tsc-alias": "^1.8.16", "typescript": "^5.8.3"}}