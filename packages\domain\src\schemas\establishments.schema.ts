import * as Schema from 'effect/Schema';
import { descriptionSchema } from './base.schema';

// — Translation shape
export const EstablishmentTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(descriptionSchema),
  otherNames: Schema.optional(Schema.String),
  acronyms: Schema.optional(Schema.String),
});

export type EstablishmentTranslation = Schema.Schema.Type<typeof EstablishmentTranslationSchema>;

// — Full Establishment shape (corresponds to institutions in DB)
export const EstablishmentSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  guidId: Schema.optional(Schema.String),       // nullable
  typeId: Schema.String,                        // required
  translations: Schema.Array(EstablishmentTranslationSchema), // at least []
  createdAt: Schema.String,                     // ISO timestamp
  updatedAt: Schema.String,                     // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),  // nullable
});

export type Establishment = Schema.Schema.Type<typeof EstablishmentSchema>;

// — Input schemas for API
export const CreateEstablishmentSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.String,
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  ),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateEstablishmentPayload = Schema.Schema.Type<typeof CreateEstablishmentSchema>;

export const UpdateEstablishmentSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  )),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateEstablishmentPayload = Schema.Schema.Type<typeof UpdateEstablishmentSchema> & { id: string };

// — Response schemas
export const EstablishmentResponseSchema = EstablishmentSchema;
export const EstablishmentListResponseSchema = Schema.Array(EstablishmentResponseSchema);

export type EstablishmentResponse = Schema.Schema.Type<typeof EstablishmentResponseSchema>;
export type EstablishmentListResponse = Schema.Schema.Type<typeof EstablishmentListResponseSchema>;
