import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class BuildingsRepositoryLive extends Effect.Service<BuildingsRepositoryLive>()(
  'BuildingsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllBuildings = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.buildings.findMany({
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const findBuildingById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.buildings.findFirst({
            where: eq(DBSchema.buildings.id, id),
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const createBuilding = dbClient.makeQuery(
        (
          execute,
          params: {
            campusId?: string | null;
            civicAddressId?: string | null;
            sadId?: string | null;
            diId?: string | null;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) => {
            return client
              .insert(DBSchema.buildings)
              .values({
                ...params,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })
              .returning({
                id: DBSchema.buildings.id,
                campusId: DBSchema.buildings.campusId,
                civicAddressId: DBSchema.buildings.civicAddressId,
                sadId: DBSchema.buildings.sadId,
                diId: DBSchema.buildings.diId,
                createdAt: DBSchema.buildings.createdAt,
                updatedAt: DBSchema.buildings.updatedAt,
                modifiedBy: DBSchema.buildings.modifiedBy,
              });
          });
        },
      );

      const updateBuilding = dbClient.makeQuery(
        (
          execute,
          data: {
            id: string;
            campusId?: string | null;
            civicAddressId?: string | null;
            sadId?: string | null;
            diId?: string | null;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) =>
            client
              .update(DBSchema.buildings)
              .set({
                campusId: data.campusId,
                civicAddressId: data.civicAddressId,
                sadId: data.sadId,
                diId: data.diId,
                modifiedBy: data.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.buildings.id, data.id))
              .returning({
                id: DBSchema.buildings.id,
                campusId: DBSchema.buildings.campusId,
                civicAddressId: DBSchema.buildings.civicAddressId,
                sadId: DBSchema.buildings.sadId,
                diId: DBSchema.buildings.diId,
                createdAt: DBSchema.buildings.createdAt,
                updatedAt: DBSchema.buildings.updatedAt,
                modifiedBy: DBSchema.buildings.modifiedBy,
              }),
          );
        },
      );

      const deleteBuilding = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.buildings)
            .where(eq(DBSchema.buildings.id, id))
            .returning({ id: DBSchema.buildings.id }),
        );
      }); // Method to create building with translations
      const createBuildingWithTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            building: {
              campusId?: string | null;
              civicAddressId?: string | null;
              sadId?: string | null;
              diId?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
            }>;
          },
        ) => {
          return execute(async (client) => {
            // First create the building
            const [building] = await client
              .insert(DBSchema.buildings)
              .values({
                ...params.building,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })
              .returning({
                id: DBSchema.buildings.id,
                campusId: DBSchema.buildings.campusId,
                civicAddressId: DBSchema.buildings.civicAddressId,
                sadId: DBSchema.buildings.sadId,
                diId: DBSchema.buildings.diId,
                createdAt: DBSchema.buildings.createdAt,
                updatedAt: DBSchema.buildings.updatedAt,
                modifiedBy: DBSchema.buildings.modifiedBy,
              }); // Then create the translations if provided
            if (params.translations.length > 0) {
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: building.id,
                  ...translation,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }),
              );
              await client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert);
            }

            return building;
          });
        },
      ); // Method to update building translations
      const updateBuildingTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            buildingId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) => {
          return execute(async (client) => {
            // Delete existing translations
            await client
              .delete(DBSchema.buildingsI18N)
              .where(eq(DBSchema.buildingsI18N.dataId, params.buildingId)); // Insert new translations
            if (params.translations.length > 0) {
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: params.buildingId,
                  ...translation,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }),
              );
              return await client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.buildingsI18N.id,
                  dataId: DBSchema.buildingsI18N.dataId,
                  locale: DBSchema.buildingsI18N.locale,
                  name: DBSchema.buildingsI18N.name,
                  description: DBSchema.buildingsI18N.description,
                  otherNames: DBSchema.buildingsI18N.otherNames,
                });
            }

            return [];
          });
        },
      );

      // Method to update building with translations
      const updateBuildingWithTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            buildingId: string;
            building: {
              campusId?: string | null;
              civicAddressId?: string | null;
              sadId?: string | null;
              diId?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) => {
          return execute(async (client) => {
            // First update the building
            const [building] = await client
              .update(DBSchema.buildings)
              .set({
                ...params.building,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.buildings.id, params.buildingId))
              .returning({
                id: DBSchema.buildings.id,
                campusId: DBSchema.buildings.campusId,
                civicAddressId: DBSchema.buildings.civicAddressId,
                sadId: DBSchema.buildings.sadId,
                diId: DBSchema.buildings.diId,
                createdAt: DBSchema.buildings.createdAt,
                updatedAt: DBSchema.buildings.updatedAt,
                modifiedBy: DBSchema.buildings.modifiedBy,
              }); // Then update translations
            if (params.translations.length > 0) {
              // Delete existing translations
              await client
                .delete(DBSchema.buildingsI18N)
                .where(eq(DBSchema.buildingsI18N.dataId, params.buildingId));

              // Insert new translations
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: params.buildingId,
                  ...translation,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }),
              );

              await client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert);
            }

            return [building];
          });
        },
      );

      return {
        findAllBuildings,
        findBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
        createBuildingWithTranslations,
        updateBuildingTranslations,
        updateBuildingWithTranslations,
      } as const;
    }),
  },
) {}
