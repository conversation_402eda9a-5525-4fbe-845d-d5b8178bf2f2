import type {
  CollectionQuerySchema,
  CollectionViewSchema,
  LocaleSchema,
  ResourceQuerySchema,
} from '@/schemas';
import type * as DbSchema from '@rie/db-schema/schemas';
import type * as Option from 'effect/Option';
import type * as Schema from 'effect/Schema';

export type SortableField = string;
export type DefaultSortableColumn = 'updatedAt';

export type Sort = 'asc' | 'desc';

export type SortOptions<TColumn extends string = string> = {
  column: TColumn;
  order: Sort;
};

export type PaginationOptions = {
  page: number;
  limit: number;
};

export type CollectionQuerySchemaType = Schema.Schema.Type<
  typeof CollectionQuerySchema
>;

export type CollectionViewType = CollectionQuerySchemaType['view'];

export type ResourceQuerySchemaType = Schema.Schema.Type<
  typeof ResourceQuerySchema
>;

export type ResourceViewType = ResourceQuerySchemaType['view'];

export type Pagination = {
  limit: number;
  offset: number;
};

export type QueryConfig<TColumn extends string = string> = {
  sort: SortOptions<TColumn>;
  locale: Schema.Schema.Type<typeof LocaleSchema>;
  view?: Schema.Schema.Type<typeof CollectionViewSchema>;
  pagination: Option.Option<Pagination>;
};

export type ControlledListTables = Extract<
  keyof typeof DbSchema,
  'people' | 'units'
>;

export type ControlledListQueryConfig = {
  controlledLists: ControlledListTables[];
};
