import {
  addresses,
  buildings,
  institutions,
  locales,
  serviceOffers,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const campuses = pgTable(
  'campuses',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    sadId: text(),
    institutionId: text()
      .notNull()
      .references(() => institutions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uniqueSadId: unique().on(table.sadId),
    },
  ],
);

export const campusesRelations = relations(campuses, ({ one, many }) => ({
  buildings: many(buildings),
  institution: one(institutions, {
    fields: [campuses.institutionId],
    references: [institutions.id],
  }),
  translations: many(campusesI18N),
  addresses: many(addresses),
  serviceOffers: many(serviceOffers),
}));

export const campusesI18N = pgTable(
  'campuses_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => campuses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const campusesI18NRelations = relations(campusesI18N, ({ one }) => ({
  campus: one(campuses, {
    fields: [campusesI18N.dataId],
    references: [campuses.id],
  }),
  locale: one(locales, {
    fields: [campusesI18N.locale],
    references: [locales.code],
  }),
}));

export const campusesAddressesRelations = relations(addresses, ({ one }) => ({
  campus: one(campuses, {
    fields: [addresses.id],
    references: [campuses.id],
  }),
  building: one(buildings, {
    fields: [addresses.id],
    references: [buildings.id],
  }),
  serviceOffer: one(serviceOffers, {
    fields: [addresses.id],
    references: [serviceOffers.address],
  }),
}));
