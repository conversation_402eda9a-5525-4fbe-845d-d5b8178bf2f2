import { users } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
  uniqueIndex,
} from 'drizzle-orm/pg-core';

// Define the context type enum
export const contextTypeEnum = pgEnum('context_type', [
  'institution',
  'unit',
  'infrastructure',
  'equipment',
  'none', // For roles that don't require context
]);

export const domainEnum = pgEnum('permission_domain', [
  'address',
  'applicationSector',
  'building',
  'campus',
  'equipment',
  'excellenceHub',
  'fundingProject',
  'infrastructure',
  'innovationLab',
  'institution',
  'media',
  'people',
  'researchField',
  'room',
  'serviceContract',
  'serviceOffer',
  'technique',
  'unit',
  'vendor',
  'visibility',
]);

export const permissionActionEnum = pgEnum('permission_action', [
  'create',
  'read',
  'update',
  'delete',
]);

export const permissions = pgTable(
  'permissions',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    domain: domainEnum().notNull(), // e.g., "equipment"
    action: permissionActionEnum().notNull(), // e.g., "read"
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
  },
  (table) => [
    {
      unq: unique().on(table.domain, table.action),
    },
  ],
);

export const roles = pgTable(
  'roles',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    name: text().notNull().unique(),
    description: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
  },
  (table) => [
    {
      // Create a unique constraint on the name
      unq: uniqueIndex().on(DbUtils.lower(table.name)),
    },
  ],
);

// Junction table for role permissions
export const rolePermissions = pgTable(
  'role_permissions',
  {
    roleId: text()
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
    permissionId: text()
      .notNull()
      .references(() => permissions.id, { onDelete: 'cascade' }),
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
    },
  ],
);

// Junction table for user roles with context
export const userRoles = pgTable(
  'user_roles',
  {
    userId: text()
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    roleId: text()
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
    contextType: contextTypeEnum().notNull().default('none'),
    contextId: text(), // Optional for 'none' context type
    grantedBy: text().references(() => users.id),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
  },
  (table) => [
    {
      pk: primaryKey({
        columns: [table.userId, table.roleId],
      }),
    },
  ],
);

// Direct user permissions (for custom permissions)
export const userPermissions = pgTable(
  'user_permissions',
  {
    userId: text()
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    permissionId: text()
      .notNull()
      .references(() => permissions.id, { onDelete: 'cascade' }),
    granted: boolean().notNull().default(true), // true = grant, false = deny (overrides role permissions)
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.userId, table.permissionId] }),
    },
  ],
);

// Role inheritance table
export const roleInheritance = pgTable(
  'role_inheritance',
  {
    childRoleId: text()
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
    parentRoleId: text()
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.childRoleId, table.parentRoleId] }),
    },
  ],
);

// Permission groups table
export const permissionGroups = pgTable('permission_groups', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  name: text().notNull().unique(),
  description: text(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
});

// Junction table for permission groups
export const permissionGroupPermissions = pgTable(
  'permission_group_permissions',
  {
    groupId: text()
      .notNull()
      .references(() => permissionGroups.id, { onDelete: 'cascade' }),
    permissionId: text()
      .notNull()
      .references(() => permissions.id, { onDelete: 'cascade' }),
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.groupId, table.permissionId] }),
    },
  ],
);

// Junction table for role permission groups
export const rolePermissionGroups = pgTable(
  'role_permission_groups',
  {
    roleId: text()
      .notNull()
      .references(() => roles.id, { onDelete: 'cascade' }),
    groupId: text()
      .notNull()
      .references(() => permissionGroups.id, { onDelete: 'cascade' }),
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.roleId, table.groupId] }),
    },
  ],
);

// Define relations
export const permissionsRelations = relations(permissions, ({ many }) => ({
  rolePermissions: many(rolePermissions),
  userPermissions: many(userPermissions),
}));

export const rolesRelations = relations(roles, ({ many }) => ({
  rolePermissions: many(rolePermissions),
  userRoles: many(userRoles),
  childRoles: many(roleInheritance, { relationName: 'parentRoles' }),
  parentRoles: many(roleInheritance, { relationName: 'childRoles' }),
}));

export const rolePermissionsRelations = relations(
  rolePermissions,
  ({ one }) => ({
    role: one(roles, {
      fields: [rolePermissions.roleId],
      references: [roles.id],
    }),
    permission: one(permissions, {
      fields: [rolePermissions.permissionId],
      references: [permissions.id],
    }),
  }),
);

export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(users, {
    fields: [userRoles.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [userRoles.roleId],
    references: [roles.id],
  }),
  grantedByUser: one(users, {
    fields: [userRoles.grantedBy],
    references: [users.id],
  }),
}));

export const userPermissionsRelations = relations(
  userPermissions,
  ({ one }) => ({
    user: one(users, {
      fields: [userPermissions.userId],
      references: [users.id],
    }),
    permission: one(permissions, {
      fields: [userPermissions.permissionId],
      references: [permissions.id],
    }),
  }),
);
