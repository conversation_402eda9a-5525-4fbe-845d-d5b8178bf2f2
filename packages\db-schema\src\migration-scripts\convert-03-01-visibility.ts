import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { VisibilityMigrationConverter } from './converters/03-01-convert-visibility-inserts';

async function convertVisibilityData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new VisibilityMigrationConverter();

    // Convert the file
    await converter.convertFile(inputFile, outputFile);

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertVisibilityData().catch(console.error);
