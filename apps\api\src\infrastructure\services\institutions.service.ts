import { InstitutionsRepositoryLive } from '@/infrastructure/repositories/institutions.repository';
import type {
    CreateInstitutionPayload,
    UpdateInstitutionPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class InstitutionsServiceLive extends Effect.Service<InstitutionsServiceLive>()(
    'InstitutionsServiceLive',
    {
        dependencies: [InstitutionsRepositoryLive],
        effect: Effect.gen(function* () {
            const institutionsRepository = yield* InstitutionsRepositoryLive;

            const getAllInstitutions = () => {
                return Effect.gen(function* () {
                    return yield* institutionsRepository.findAllInstitutions();
                });
            };

            const getInstitutionById = (id: string) => {
                return Effect.gen(function* () {
                    const institution = yield* institutionsRepository.findInstitutionById(id);
                    if (!institution) {
                        return yield* Effect.fail(new Error(`Institution with id ${id} not found`));
                    }
                    return institution;
                });
            };

            const createInstitution = (data: CreateInstitutionPayload) => {
                return Effect.gen(function* () {
                    return yield* institutionsRepository.createInstitutionWithTranslations({
                        institution: {
                            guidId: data.guidId || '',
                            typeId: data.typeId,
                            modifiedBy: data.modifiedBy,
                        },
                        translations: data.translations ? [...data.translations] : [],
                    });
                });
            };

            const updateInstitution = (params: UpdateInstitutionPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingInstitution = yield* institutionsRepository.findInstitutionById(id);
                    if (!existingInstitution) {
                        return yield* Effect.fail(new Error(`Institution with id ${id} not found`));
                    }

                    return yield* institutionsRepository.updateInstitution({
                        id,
                        guidId: updateData.guidId,
                        typeId: updateData.typeId,
                        modifiedBy: updateData.modifiedBy,
                    });
                });
            };

            const deleteInstitution = (id: string) => {
                return Effect.gen(function* () {
                    const existingInstitution = yield* institutionsRepository.findInstitutionById(id);
                    if (!existingInstitution) {
                        return yield* Effect.fail(new Error(`Institution with id ${id} not found`));
                    }
                    return yield* institutionsRepository.deleteInstitution(id);
                });
            };

            return {
                getAllInstitutions,
                getInstitutionById,
                createInstitution,
                updateInstitution,
                deleteInstitution,
            } as const;
        }),
    },
) { }