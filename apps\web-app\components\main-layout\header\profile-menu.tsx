'use client';

import { Connect } from '@/components/main-layout/header/connect';
import { User } from '@/components/main-layout/header/user';
import { useSession } from '@/lib/better-auth';
import { FaSpinner } from 'react-icons/fa';

export const ProfileMenu = () => {
  const { data, isPending } = useSession();

  if (isPending) {
    return <FaSpinner className="animate-spin text-xl" />;
  }

  return (
    <>
      {!data?.user ? (
        <Connect />
      ) : (
        <User email={data.user.email} fullName={data.user.name} />
      )}
    </>
  );
};
