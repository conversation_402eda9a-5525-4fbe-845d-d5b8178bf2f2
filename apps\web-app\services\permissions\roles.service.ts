import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form.schema';
import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { DbRole } from '@rie/db-schema/entity-types';

export const getAllRoles = async () => {
  try {
    const client = await getApiClient();
    return await client.get<DbRole[]>('v2/roles').json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch roles');
  }
};

export const getRoleById = async (id: string) => {
  try {
    const client = await getApiClient();
    return await client.get<DbRole>(`v2/roles/${id}`).json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch role');
  }
};

export async function createRole(payload: RoleFormSchema): Promise<DbRole> {
  try {
    const client = await getApiClient();

    const role = await client
      .post<DbRole>('v2/roles', {
        json: payload,
      })
      .json();

    // Then parse the JSON
    console.log('Role created successfully:', role);
    return role;
  } catch (error) {
    console.error('Error creating role:', error);

    if (error instanceof APIV2Error) {
      throw error;
    }

    if (error instanceof Error) {
      throw new Error(`Failed to create role: ${error.message}`);
    }

    // Otherwise, wrap it in a generic error
    throw new Error('Failed to create role: Unknown error');
  }
}

interface UpdateRoleParams {
  payload: RoleFormSchema;
  id: string;
}

export const updateRole = async ({ payload, id }: UpdateRoleParams) => {
  try {
    const apiClient = await getApiClient();

    return await apiClient
      .put<DbRole>(`v2/roles/${id}`, {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    // Otherwise, wrap it in a generic error
    throw new Error('Failed to update role');
  }
};

export const deleteRole = async (id: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(`v2/roles/${id}`);
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }

    throw new Error('Failed to delete role');
  }
};
