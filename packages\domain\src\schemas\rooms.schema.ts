import * as Schema from 'effect/Schema';

// — Full Room shape (rooms don't have translations in the DB schema)
export const RoomSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  number: Schema.String,                         // required
  area: Schema.optional(Schema.Number),         // nullable double precision
  floorLoad: Schema.optional(Schema.Number),    // nullable double precision
  buildingId: Schema.optional(Schema.String),   // nullable - references buildings
  createdAt: Schema.String,                      // ISO timestamp
  updatedAt: Schema.String,                      // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),   // nullable
});

export type Room = Schema.Schema.Type<typeof RoomSchema>;

// — Input schemas for API
export const CreateRoomSchema = Schema.Struct({
  number: Schema.String,
  area: Schema.optional(Schema.Number),
  floorLoad: Schema.optional(Schema.Number),
  buildingId: Schema.optional(Schema.String),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateRoomPayload = Schema.Schema.Type<typeof CreateRoomSchema>;

export const UpdateRoomSchema = Schema.Struct({
  number: Schema.optional(Schema.String),
  area: Schema.optional(Schema.Number),
  floorLoad: Schema.optional(Schema.Number),
  buildingId: Schema.optional(Schema.String),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateRoomPayload = Schema.Schema.Type<typeof UpdateRoomSchema> & { id: string };

// — Response schemas
export const RoomResponseSchema = RoomSchema;
export const RoomListResponseSchema = Schema.Array(RoomResponseSchema);

export type RoomResponse = Schema.Schema.Type<typeof RoomResponseSchema>;
export type RoomListResponse = Schema.Schema.Type<typeof RoomListResponseSchema>;
