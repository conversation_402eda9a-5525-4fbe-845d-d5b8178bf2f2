import { AddressTypeToggle } from '@/components/civic-campus-address/address-type-toggle';
import { CampusAddress } from '@/components/civic-campus-address/campus-address';
import { CivicAddress } from '@/components/civic-campus-address/civic-address';
import { useCivicCampusAddress } from '@/hooks/use-civic-campus-address';
import { isCampusAddress } from '@/schemas/common-schema';
import { Button } from '@/ui/button';
import { useTranslations } from 'next-intl';
import { FiTrash2 } from 'react-icons/fi';

type CivicCampusAddressProps = {
  addressFieldPath: string;
  totalAddresses: number;
  onRemoveAddress?: () => void;
  withAddressTypeToggle: boolean;
  required?: boolean;
};
export const CivicCampusAddress = ({
  addressFieldPath,
  totalAddresses,
  onRemoveAddress,
  withAddressTypeToggle,
  required = true,
}: CivicCampusAddressProps) => {
  const tCommon = useTranslations('common');
  const {
    values,
    handleCivicAddressChange,
    handleRoomChange,
    handleBuildingChange,
    handleToggleAddressType,
  } = useCivicCampusAddress(addressFieldPath);

  return (
    <div className="flex flex-col gap-4">
      {withAddressTypeToggle ? (
        <AddressTypeToggle
          onToggle={handleToggleAddressType}
          value={values.addressType}
        />
      ) : null}
      {values.addressType === 'campus' && isCampusAddress(values) ? (
        <CampusAddress
          onSelectBuildingChange={handleBuildingChange}
          onSelectRoomChange={handleRoomChange}
          buildingPlaceholder={tCommon(
            'components.address.campus.building.placeholder',
          )}
          roomPlaceholder={tCommon(
            'components.address.campus.room.placeholder',
          )}
          selectedBuilding={values.data.listedBuilding}
          selectedRoom={values.data.listedLocal}
        />
      ) : (
        <CivicAddress
          onValueChange={handleCivicAddressChange}
          required={required}
        />
      )}
      {totalAddresses > 0 && (
        <Button
          className="self-end"
          onClick={onRemoveAddress}
          type="button"
          variant="destructive"
        >
          <FiTrash2 className="mr-2 h-4 w-4" /> {tCommon('delete')}
        </Button>
      )}
    </div>
  );
};
