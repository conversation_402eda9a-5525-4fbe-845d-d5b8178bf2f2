import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { CampusI18nMigrationConverter } from './converters/29-02-convert-campus-i18n-inserts';

async function convertCampusI18nData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new CampusI18nMigrationConverter();

    // Run the conversion
    await converter.convertFile(inputFile, outputFile);
  } catch (error) {
    console.error('Error converting campus i18n data:', error);
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (require.main === module) {
  convertCampusI18nData().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { convertCampusI18nData };
