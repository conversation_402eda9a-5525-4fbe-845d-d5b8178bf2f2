import {
  type Person,
  type PersonItem,
  PersonItemSchema,
  DbPersonSchema,
} from '@/schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const DbPersonToPersonItem = Schema.transformOrFail(
  DbPersonSchema,
  PersonItemSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: `${raw.firstName} ${raw.lastName}`.trim() || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse person',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const transformPersonData = (
  data: Person[],
): PersonItem[] => {
  return data.map((person) => ({
    value: person.id,
    label: `${person.firstName} ${person.lastName}`.trim() || person.id,
  }));
};
