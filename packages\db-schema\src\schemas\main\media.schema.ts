import {
  equipmentAssociatedDocuments,
  equipmentAssociatedMedia,
  locales,
  peopleAssociatedMedia,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const media = pgTable('media', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  filename: text().notNull(),
  mimeType: text().notNull(),
  filesize: integer(),
  path: text(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const mediaRelations = relations(media, ({ one, many }) => ({
  translations: many(mediaI18N),
  peopleMedia: many(peopleAssociatedMedia),
  equipmentMedia: many(equipmentAssociatedMedia),
  equipmentDocuments: many(equipmentAssociatedDocuments),
  type: one(mediaTypes, {
    fields: [media.id],
    references: [mediaTypes.id],
  }),
}));

export const mediaI18N = pgTable(
  'media_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => media.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text().notNull(),
    name: text(),
    title: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const mediaI18NRelations = relations(mediaI18N, ({ one }) => ({
  media: one(media, {
    fields: [mediaI18N.dataId],
    references: [media.id],
  }),
  locale: one(locales, {
    fields: [mediaI18N.locale],
    references: [locales.code],
  }),
}));

export const mediaTypes = pgTable('media_types', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
});

export const mediaTypeRelations = relations(mediaTypes, ({ many }) => ({
  media: many(media),
  translations: many(mediaTypesI18N),
}));

export const mediaTypesI18N = pgTable(
  'media_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => mediaTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text().notNull(),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const mediaTypesI18NRelations = relations(mediaTypesI18N, ({ one }) => ({
  mediaType: one(mediaTypes, {
    fields: [mediaTypesI18N.dataId],
    references: [mediaTypes.id],
  }),
  locale: one(locales, {
    fields: [mediaTypesI18N.locale],
    references: [locales.code],
  }),
}));
