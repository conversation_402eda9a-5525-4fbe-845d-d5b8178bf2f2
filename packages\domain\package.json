{"name": "@rie/domain", "version": "1.0.0", "private": true, "type": "module", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "main": "./build/index.js", "exports": {".": {"import": "./build/index.js", "require": "./build/index.js", "types": "./build/dts/index.d.ts"}, "./constants": {"import": "./build/constants/index.js", "require": "./build/constants/index.js", "types": "./build/dts/constants/index.d.ts"}, "./errors": {"import": "./build/errors/index.js", "require": "./build/errors/index.js", "types": "./build/dts/errors/index.d.ts"}, "./schemas": {"import": "./build/schemas/index.js", "require": "./build/schemas/index.js", "types": "./build/dts/schemas/index.d.ts"}, "./serializers": {"import": "./build/serializers/index.js", "require": "./build/serializers/index.js", "types": "./build/dts/serializers/index.d.ts"}, "./types": {"import": "./build/types/index.js", "require": "./build/types/index.js", "types": "./build/dts/types/index.d.ts"}, "./utils": {"import": "./build/utils/index.js", "require": "./build/utils/index.js", "types": "./build/dts/utils/index.d.ts"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc -b tsconfig.build.json", "format": "pnpm biome check --write"}, "peerDependencies": {"effect": "^3.15.0"}, "devDependencies": {"@rie/biome-config": "workspace:*", "@types/node": "^22.15.17", "@types/pg": "^8.15.1", "tsc-alias": "^1.8.16", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@rie/db-schema": "workspace:*", "@rie/typescript-config": "workspace:*", "mysql2": "^3.14.1", "pg": "^8.16.0"}}