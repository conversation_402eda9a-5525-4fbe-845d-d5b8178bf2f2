import {
  type Institution,
  type InstitutionItem,
  InstitutionItemSchema,
  DbInstitutionSchema,
} from '@/schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const DbInstitutionToInstitutionItem = Schema.transformOrFail(
  DbInstitutionSchema,
  InstitutionItemSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.translations?.[0]?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse institution',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const transformInstitutionData = (
  data: Institution[],
  requestedLocale: string | null,
  fallbackLocale: string,
): InstitutionItem[] => {
  // Group by ID
  const itemsById = data.reduce(
    (acc, item) => {
      if (!acc[item.id]) {
        acc[item.id] = [item];
      } else {
        acc[item.id]?.push(item);
      }
      return acc;
    },
    {} as Record<string, typeof data>,
  );

  // For each ID, select the right locale
  return Object.entries(itemsById).map(([id, items]) => {
    // Get all translations from all items with this ID
    const allTranslations = items.flatMap(item => item.translations || []);
    
    // Try to find the requested locale
    const requestedTranslation = allTranslations.find((t) => t.locale === requestedLocale);

    // If not found, try to find the fallback locale
    const fallbackTranslation = !requestedTranslation
      ? allTranslations.find((t) => t.locale === fallbackLocale)
      : null;

    // Use requested locale if available, then fallback locale, then any translation
    const selectedTranslation = requestedTranslation || fallbackTranslation || allTranslations[0];

    return {
      value: id,
      label: selectedTranslation?.name || id,
    };
  });
};
