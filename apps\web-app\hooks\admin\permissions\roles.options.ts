import { getAllRoles, getRoleById } from '@/services/permissions/roles.service';
import { queryOptions } from '@tanstack/react-query';

export const getAllRolesOptions = () => {
  return queryOptions({
    queryFn: getAllRoles,
    queryKey: ['roles'],
    select: (data) =>
      data.map((role) => ({
        label: role.name,
        value: role.id,
      })),
  });
};

export const getRoleByIdOptions = (id: string) => {
  return queryOptions({
    queryFn: () => getRoleById(id),
    queryKey: ['roles', id],
    enabled: !!id,
  });
};
