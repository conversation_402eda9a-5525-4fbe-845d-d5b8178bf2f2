import {
  applicationSectors,
  campusAddresses,
  documentationCategories,
  excellenceHubs,
  fundingProjects,
  guids,
  infrastructures,
  institutions,
  locales,
  media,
  people,
  researchFields,
  serviceContracts,
  serviceOffersEquipments,
  techniques,
  users,
  vendors,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  doublePrecision,
  integer,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const equipments = pgTable(
  'equipments',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    guidId: text().references(() => guids.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    campusAddressId: text().references(() => campusAddresses.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    isCampusAddressConfidential: boolean().default(false),
    model: text(),
    serialNumber: text(),
    homologationNumber: text(),
    inventoryNumber: text(),
    doi: text(),
    useInClinicalTrial: boolean().notNull().default(false),
    isHidden: boolean().notNull().default(false),
    typeId: text()
      .notNull()
      .references(() => equipmentTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    statusId: text().references(() => equipmentStatuses.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    workingPercentage: doublePrecision(),
    monetaryCost: doublePrecision(),
    inKindCost: doublePrecision(),
    manufactureYear: integer(),
    acquisitionDate: date({ mode: 'string' }),
    installationDate: date({ mode: 'string' }),
    decommissioningDate: date({ mode: 'string' }),
    scientificManagerId: text().references(() => people.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    manufacturerId: text().references(() => vendors.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    supplierId: text().references(() => vendors.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    infrastructureId: text().references(() => infrastructures.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    isFeatured: boolean().default(false),
    institutionId: text().references(() => institutions.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      guidIdUnique: unique().on(table.guidId),
      serialNumberUnique: unique().on(table.serialNumber),
      homologationNumberUnique: unique().on(table.homologationNumber),
      inventoryNumberUnique: unique().on(table.inventoryNumber),
      doiUnique: unique().on(table.doi),
    },
  ],
);

export const equipmentRelations = relations(equipments, ({ one, many }) => ({
  guid: one(guids, {
    fields: [equipments.guidId],
    references: [guids.id],
  }),
  scientificManager: one(people, {
    fields: [equipments.scientificManagerId],
    references: [people.id],
  }),
  campus: one(campusAddresses, {
    fields: [equipments.campusAddressId],
    references: [campusAddresses.id],
  }),
  manufacturer: one(vendors, {
    fields: [equipments.manufacturerId],
    references: [vendors.id],
    relationName: 'equipment_to_manufacturer',
  }),
  supplier: one(vendors, {
    fields: [equipments.supplierId],
    references: [vendors.id],
    relationName: 'equipment_to_supplier',
  }),
  institution: one(institutions, {
    fields: [equipments.institutionId],
    references: [institutions.id],
  }),
  status: one(equipmentStatuses, {
    fields: [equipments.statusId],
    references: [equipmentStatuses.id],
  }),
  type: one(equipmentTypes, {
    fields: [equipments.typeId],
    references: [equipmentTypes.id],
  }),
  infrastructure: one(infrastructures, {
    fields: [equipments.infrastructureId],
    references: [infrastructures.id],
  }),
  translations: many(equipmentsI18N),
  techniques: many(equipmentAssociatedTechniques),
  categories: many(equipmentAssociatedCategories),
  maintenances: many(equipmentAssociatedMaintenances),
  applicationSectors: many(equipmentAssociatedApplicationSectors),
  excellenceHubs: many(equipmentAssociatedExcellenceHubs),
  fundingProjects: many(equipmentAssociatedFundingProjects),
  lifecycles: many(equipmentAssociatedLifecycles), // TODO: Check with Sakinah if this should be a one to one relation
  dimensions: many(equipmentAssociatedDimensions),
  media: many(equipmentAssociatedMedia),
  repairers: many(equipmentAssociatedRepairers, {
    relationName: 'equipment_to_repairers',
  }),
  retailers: many(equipmentAssociatedRetailers, {
    relationName: 'equipment_to_retailers',
  }),
  serviceOffers: many(serviceOffersEquipments),
  serviceContracts: many(serviceContracts),
  researchFields: many(equipmentAssociatedResearchFields),
  // Equipment that this item is linked to (as a component or accessory)
  linkedToEquipments: many(equipmentAssociatedRelationships, {
    relationName: 'linkedToEquipment',
  }),
  // Equipment linked to this equipment (components or accessories)
  linkedEquipments: many(equipmentAssociatedRelationships, {
    relationName: 'equipmentLinks',
  }),
}));

export const equipmentsI18N = pgTable(
  'equipments_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    specification: text(),
    usageContext: text(),
    risk: text(),
    comment: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const equipmentsI18NRelations = relations(equipmentsI18N, ({ one }) => ({
  equipment: one(equipments, {
    fields: [equipmentsI18N.dataId],
    references: [equipments.id],
  }),
  locale: one(locales, {
    fields: [equipmentsI18N.locale],
    references: [locales.code],
  }),
}));

export const equipmentAssociatedApplicationSectors = pgTable(
  'equipment_associated_application_sectors',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    applicationSectorId: text()
      .notNull()
      .references(() => applicationSectors.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentApplicationSectorPk: primaryKey({
        columns: [table.equipmentId, table.applicationSectorId],
      }),
    },
  ],
);

export const equipmentAssociatedApplicationSectorsRelations = relations(
  equipmentAssociatedApplicationSectors,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedApplicationSectors.equipmentId],
      references: [equipments.id],
    }),
    applicationSector: one(applicationSectors, {
      fields: [equipmentAssociatedApplicationSectors.applicationSectorId],
      references: [applicationSectors.id],
    }),
  }),
);

export const equipmentAssociatedCategories = pgTable(
  'equipment_associated_categories',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    categoryId: text()
      .notNull()
      .references(() => equipmentCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentCategoryPk: primaryKey({
        columns: [table.equipmentId, table.categoryId],
      }),
    },
  ],
);

export const equipmentAssociatedTechniques = pgTable(
  'equipment_associated_techniques',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    techniqueId: text()
      .notNull()
      .references(() => techniques.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentTechniquePk: primaryKey({
        columns: [table.equipmentId, table.techniqueId],
      }),
    },
  ],
);

export const equipmentAssociatedTechniquesRelations = relations(
  equipmentAssociatedTechniques,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedTechniques.equipmentId],
      references: [equipments.id],
    }),
    technique: one(techniques, {
      fields: [equipmentAssociatedTechniques.techniqueId],
      references: [techniques.id],
    }),
  }),
);

export const equipmentAssociatedCategoriesRelations = relations(
  equipmentAssociatedCategories,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedCategories.equipmentId],
      references: [equipments.id],
    }),
    category: one(equipmentCategories, {
      fields: [equipmentAssociatedCategories.categoryId],
      references: [equipmentCategories.id],
    }),
  }),
);

export const equipmentCategories = pgTable(
  'equipment_categories',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    uid: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const equipmentCategoriesRelations = relations(
  equipmentCategories,
  ({ many }) => ({
    translations: many(equipmentCategoriesI18N),
    equipments: many(equipmentAssociatedCategories),
    children: many(equipmentCategoryParents, { relationName: 'children' }),
    parents: many(equipmentCategoryParents, { relationName: 'parents' }),
  }),
);

export const equipmentCategoriesI18N = pgTable(
  'equipment_categories_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const equipmentCategoriesI18NRelations = relations(
  equipmentCategoriesI18N,
  ({ one }) => ({
    category: one(equipmentCategories, {
      fields: [equipmentCategoriesI18N.dataId],
      references: [equipmentCategories.id],
    }),
    locale: one(locales, {
      fields: [equipmentCategoriesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const equipmentCategoryParents = pgTable(
  'equipment_category_parents',
  {
    childId: text()
      .notNull()
      .references(() => equipmentCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    parentId: text()
      .notNull()
      .references(() => equipmentCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentCategoriesParentsPk: primaryKey({
        columns: [table.childId, table.parentId],
      }),
    },
  ],
);

export const equipmentCategoryParentsRelations = relations(
  equipmentCategoryParents,
  ({ one }) => ({
    child: one(equipmentCategories, {
      fields: [equipmentCategoryParents.childId],
      references: [equipmentCategories.id],
      relationName: 'children',
    }),
    parent: one(equipmentCategories, {
      fields: [equipmentCategoryParents.parentId],
      references: [equipmentCategories.id],
      relationName: 'parents',
    }),
  }),
);

export const equipmentRelationships = pgTable('equipment_relationships', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  typeId: text()
    .notNull()
    .references(() => equipmentRelationshipTypes.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const equipmentRelationshipsRelations = relations(
  equipmentRelationships,
  ({ one, many }) => ({
    translations: many(equipmentRelationshipsI18N),
    relationshipType: one(equipmentRelationshipTypes, {
      fields: [equipmentRelationships.typeId],
      references: [equipmentRelationshipTypes.id],
    }),
  }),
);

export const equipmentRelationshipsI18N = pgTable(
  'equipment_relationships_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentRelationships.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      dataIdUnique: unique().on(table.dataId, table.locale),
      localeIdUnique: unique().on(table.locale, table.name),
    },
  ],
);

export const equipmentRelationshipsI18NRelations = relations(
  equipmentRelationshipsI18N,
  ({ one }) => ({
    relationship: one(equipmentRelationships, {
      fields: [equipmentRelationshipsI18N.dataId],
      references: [equipmentRelationships.id],
    }),
    locale: one(locales, {
      fields: [equipmentRelationshipsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const equipmentRelationshipTypes = pgTable(
  'equipment_relationship_types',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
);

export const equipmentRelationshipTypesRelations = relations(
  equipmentRelationshipTypes,
  ({ many }) => ({
    translations: many(equipmentRelationshipTypesI18N),
    relationships: many(equipmentRelationships),
  }),
);

export const equipmentRelationshipTypesI18N = pgTable(
  'equipment_relationship_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentRelationshipTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const equipmentRelationshipTypesI18NRelations = relations(
  equipmentRelationshipTypesI18N,
  ({ one }) => ({
    relationshipType: one(equipmentRelationshipTypes, {
      fields: [equipmentRelationshipTypesI18N.dataId],
      references: [equipmentRelationshipTypes.id],
    }),
    locale: one(locales, {
      fields: [equipmentRelationshipTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const equipmentAssociatedRelationships = pgTable(
  'equipment_associated_relationships',
  {
    equipmentId: text('equipment_id')
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    relationshipId: text('related_equipment_id')
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    relationshipTypeId: text()
      .notNull()
      .references(() => equipmentRelationshipTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => ({
    pk: primaryKey({
      columns: [
        table.equipmentId,
        table.relationshipId,
        table.relationshipTypeId,
      ],
    }),
  }),
);

export const equipmentAssociatedDependenciesRelations = relations(
  equipmentAssociatedRelationships,
  ({ one }) => ({
    // The main equipment that has linked equipment
    mainEquipment: one(equipments, {
      fields: [equipmentAssociatedRelationships.equipmentId],
      references: [equipments.id],
      relationName: 'equipmentLinks',
    }),

    // The linked equipment (component or accessory)
    linkedEquipment: one(equipments, {
      fields: [equipmentAssociatedRelationships.relationshipId],
      references: [equipments.id],
      relationName: 'linkedToEquipment',
    }),

    // Relationship type reference
    relationshipType: one(equipmentRelationshipTypes, {
      fields: [equipmentAssociatedRelationships.relationshipTypeId],
      references: [equipmentRelationshipTypes.id],
    }),
  }),
);

export const equipmentAssociatedDimensions = pgTable(
  'equipment_associated_dimensions',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    value: doublePrecision().notNull(),
    unitId: text()
      .notNull()
      .references(() => equipmentMeasurementUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
);

export const equipmentAssociatedDimensionsRelations = relations(
  equipmentAssociatedDimensions,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedDimensions.equipmentId],
      references: [equipments.id],
    }),
    unit: one(equipmentMeasurementUnits, {
      fields: [equipmentAssociatedDimensions.unitId],
      references: [equipmentMeasurementUnits.id],
    }),
  }),
);

export const equipmentAssociatedDocuments = pgTable(
  'equipment_associated_documents',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    documentId: text()
      .notNull()
      .references(() => media.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    categoryId: text().references(() => documentationCategories.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  },
  (table) => [
    {
      uniqueEquipmentDocument: unique().on(table.equipmentId, table.documentId),
    },
  ],
);

export const equipmentAssociatedDocumentRelations = relations(
  equipmentAssociatedDocuments,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedDocuments.equipmentId],
      references: [equipments.id],
    }),
    document: one(media, {
      fields: [equipmentAssociatedDocuments.documentId],
      references: [media.id],
    }),
    category: one(documentationCategories, {
      fields: [equipmentAssociatedDocuments.categoryId],
      references: [documentationCategories.id],
    }),
  }),
);

export const equipmentAssociatedExcellenceHubs = pgTable(
  'equipment_associated_excellence_hubs',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    excellenceHubId: text()
      .notNull()
      .references(() => excellenceHubs.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentExcellenceHubPk: primaryKey({
        columns: [table.equipmentId, table.excellenceHubId],
      }),
    },
  ],
);

export const equipmentAssociatedExcellenceHubsRelations = relations(
  equipmentAssociatedExcellenceHubs,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedExcellenceHubs.equipmentId],
      references: [equipments.id],
    }),
    excellenceHub: one(excellenceHubs, {
      fields: [equipmentAssociatedExcellenceHubs.excellenceHubId],
      references: [excellenceHubs.id],
    }),
  }),
);

export const equipmentAssociatedFundingProjects = pgTable(
  'equipment_associated_funding_projects',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    fundingProjectId: text()
      .notNull()
      .references(() => fundingProjects.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentFundingProjectPk: primaryKey({
        columns: [table.equipmentId, table.fundingProjectId],
      }),
    },
  ],
);

export const equipmentFundingProjectsRelations = relations(
  equipmentAssociatedFundingProjects,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedFundingProjects.equipmentId],
      references: [equipments.id],
    }),
    fundingProject: one(fundingProjects, {
      fields: [equipmentAssociatedFundingProjects.fundingProjectId],
      references: [fundingProjects.id],
    }),
  }),
);

export const equipmentAssociatedLifecycles = pgTable(
  'equipment_associated_lifecycles',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    value: integer().notNull(),
    timeUnitId: text()
      .notNull()
      .references(() => timeUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
);

export const equipmentAssociatedLifecyclesRelations = relations(
  equipmentAssociatedLifecycles,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedLifecycles.equipmentId],
      references: [equipments.id],
    }),
    timeUnit: one(timeUnits, {
      fields: [equipmentAssociatedLifecycles.timeUnitId],
      references: [timeUnits.id],
    }),
  }),
);

export const equipmentAssociatedMaintenances = pgTable(
  'equipment_associated_maintenance_frequency',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    value: doublePrecision().notNull(),
    timeUnitId: text()
      .notNull()
      .references(() => timeUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentFrequencyPk: primaryKey({
        columns: [table.equipmentId, table.timeUnitId],
      }),
    },
  ],
);

export const equipmentAssociatedMaintenancesRelations = relations(
  equipmentAssociatedMaintenances,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedMaintenances.equipmentId],
      references: [equipments.id],
    }),
    timeUnit: one(timeUnits, {
      fields: [equipmentAssociatedMaintenances.timeUnitId],
      references: [timeUnits.id],
    }),
  }),
);

export const equipmentAssociatedOperationalManagers = pgTable(
  'equipment_associated_operational_managers',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueEquipmentPerson: primaryKey({
        columns: [table.equipmentId, table.personId],
      }),
    },
  ],
);

export const equipmentAssociatedOperationalManagersRelations = relations(
  equipmentAssociatedOperationalManagers,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedOperationalManagers.equipmentId],
      references: [equipments.id],
    }),
    person: one(people, {
      fields: [equipmentAssociatedOperationalManagers.personId],
      references: [people.id],
    }),
  }),
);

export const equipmentAssociatedSSTManagers = pgTable(
  'equipment_associated_sst_managers',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueEquipmentPerson: primaryKey({
        columns: [table.equipmentId, table.personId],
      }),
    },
  ],
);

export const equipmentAssociatedSSTManagersRelations = relations(
  equipmentAssociatedSSTManagers,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedSSTManagers.equipmentId],
      references: [equipments.id],
    }),
    person: one(people, {
      fields: [equipmentAssociatedSSTManagers.personId],
      references: [people.id],
    }),
  }),
);

export const equipmentMeasurementPrecisions = pgTable(
  'equipment_measurement_precisions',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    value: doublePrecision().notNull(),
    measurementUnitId: text()
      .notNull()
      .references(() => equipmentMeasurementUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
);

export const equipmentMeasurementPrecisionsRelations = relations(
  equipmentMeasurementPrecisions,
  ({ one, many }) => ({
    equipment: one(equipments, {
      fields: [equipmentMeasurementPrecisions.id],
      references: [equipments.id],
    }),
  }),
);

export const equipmentAssociatedMeasurementPrecisions = pgTable(
  'equipment_associated_measurement_precisions',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    equipmentPrecisionId: text()
      .notNull()
      .references(() => equipmentMeasurementPrecisions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueEquipmentPrecision: primaryKey({
        columns: [table.equipmentId, table.equipmentPrecisionId],
      }),
    },
  ],
);

export const equipmentAssociatedMeasurementPrecisionsRelations = relations(
  equipmentAssociatedMeasurementPrecisions,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedMeasurementPrecisions.equipmentId],
      references: [equipments.id],
    }),
    precision: one(equipmentMeasurementPrecisions, {
      fields: [equipmentAssociatedMeasurementPrecisions.equipmentPrecisionId],
      references: [equipmentMeasurementPrecisions.id],
    }),
  }),
);

export const equipmentAssociatedRepairers = pgTable(
  'equipment_associated_repairers',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    vendorId: text()
      .notNull()
      .references(() => vendors.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueEquipmentVendor: primaryKey({
        columns: [table.equipmentId, table.vendorId],
      }),
    },
  ],
);

export const equipmentAssociatedRepairersRelations = relations(
  equipmentAssociatedRepairers,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedRepairers.equipmentId],
      references: [equipments.id],
      relationName: 'equipment_to_repairers',
    }),
    repairer: one(vendors, {
      fields: [equipmentAssociatedRepairers.vendorId],
      references: [vendors.id],
      relationName: 'repairer_to_equipment',
    }),
  }),
);

export const equipmentAssociatedResearchFields = pgTable(
  'equipment_associated_research_fields',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    researchFieldId: text()
      .notNull()
      .references(() => researchFields.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentResearchFieldPk: primaryKey({
        columns: [table.equipmentId, table.researchFieldId],
      }),
    },
  ],
);

export const equipmentAssociatedRetailers = pgTable(
  'equipment_associated_retailers',
  {
    equipmentId: text().references(() => vendors.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    retailerId: text().references(() => vendors.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  },
  (table) => [
    {
      equipmentRetailerPk: primaryKey({
        columns: [table.equipmentId, table.retailerId],
      }),
    },
  ],
);

export const equipmentRetailersRelations = relations(
  equipmentAssociatedRetailers,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedRetailers.equipmentId],
      references: [equipments.id],
      relationName: 'equipment_to_retailers',
    }),
    retailer: one(vendors, {
      fields: [equipmentAssociatedRetailers.retailerId],
      references: [vendors.id],
      relationName: 'retailer_to_equipment',
    }),
  }),
);

export const equipmentStatuses = pgTable('equipment_statuses', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const equipmentStatusesRelations = relations(
  equipmentStatuses,
  ({ many }) => ({
    translations: many(equipmentStatusesI18N),
  }),
);

export const equipmentStatusesI18N = pgTable(
  'equipment_statuses_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentStatuses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      dataIdUnique: unique().on(table.dataId, table.locale),
      localeIdUnique: unique().on(table.locale, table.name),
    },
  ],
);

export const equipmentStatusesI18NRelations = relations(
  equipmentStatusesI18N,
  ({ one }) => ({
    status: one(equipmentStatuses, {
      fields: [equipmentStatusesI18N.dataId],
      references: [equipmentStatuses.id],
    }),
    locale: one(locales, {
      fields: [equipmentStatusesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const equipmentAssociatedMedia = pgTable(
  'equipment_associated_media',
  {
    equipmentId: text()
      .notNull()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    mediaId: text()
      .notNull()
      .references(() => media.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      equipmentMediaPk: primaryKey({
        columns: [table.equipmentId, table.mediaId],
      }),
    },
  ],
);

export const equipmentAssociatedMediaRelations = relations(
  equipmentAssociatedMedia,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedMedia.equipmentId],
      references: [equipments.id],
    }),
    media: one(media, {
      fields: [equipmentAssociatedMedia.mediaId],
      references: [media.id],
    }),
  }),
);

// TODO: Check with Sakinah if this table is needed.
// export const equipmentAssociatedVideos = pgTable(
//   'equipment_video',
//   {
//     equipmentId: text()
//       .notNull()
//       .references(() => equipments.id, {
//         onDelete: 'cascade',
//         onUpdate: 'cascade',
//       }),
//     videoId: text()
//       .notNull()
//       .references(() => media.id, {
//         onDelete: 'cascade',
//         onUpdate: 'cascade',
//       }),
//   },
//   (table) => [
//     {
//       equipmentVideoPk: primaryKey({
//         columns: [table.equipmentId, table.videoId],
//       }),
//     },
//   ],
// );

// export const equipmentVideoRelations = relations(
//   equipmentAssociatedVideos,
//   ({ one }) => ({
//     equipment: one(equipments, {
//       fields: [equipmentAssociatedVideos.equipmentId],
//       references: [equipments.id],
//     }),
//     video: one(media, {
//       fields: [equipmentAssociatedVideos.videoId],
//       references: [media.id],
//     }),
//   }),
// );

export const equipmentMeasurementUnits = pgTable(
  'equipment_measurement_units',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
);

export const measurementUnitRelations = relations(
  equipmentMeasurementUnits,
  ({ many }) => ({
    translations: many(equipmentMeasurementUnitsI18N),
  }),
);

export const equipmentMeasurementUnitsI18N = pgTable(
  'equipment_measurement_units_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentMeasurementUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const measurementUnitsI18NRelations = relations(
  equipmentMeasurementUnitsI18N,
  ({ one }) => ({
    measurementUnit: one(equipmentMeasurementUnits, {
      fields: [equipmentMeasurementUnitsI18N.dataId],
      references: [equipmentMeasurementUnits.id],
    }),
    locale: one(locales, {
      fields: [equipmentMeasurementUnitsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const timeUnits = pgTable('time_units', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const timeUnitRelations = relations(timeUnits, ({ many }) => ({
  equipmentLifecycles: many(equipmentAssociatedLifecycles),
  translations: many(timeUnitsI18N),
}));

export const timeUnitsI18N = pgTable(
  'time_units_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => timeUnits.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    symbol: text(),
    description: text(),
  },
  (table) => [
    {
      dataIdUnique: unique().on(table.dataId, table.locale),
      localeIdUnique: unique().on(table.locale, table.name),
    },
  ],
);

export const timeUnitI18NRelations = relations(timeUnitsI18N, ({ one }) => ({
  timeUnit: one(timeUnits, {
    fields: [timeUnitsI18N.dataId],
    references: [timeUnits.id],
  }),
  locale: one(locales, {
    fields: [timeUnitsI18N.locale],
    references: [locales.code],
  }),
}));

export const equipmentTypes = pgTable('equipment_types', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const equipmentTypesRelations = relations(
  equipmentTypes,
  ({ many }) => ({
    equipments: many(equipments),
    translations: many(equipmentTypesI18N),
  }),
);

export const equipmentTypesI18N = pgTable(
  'equipment_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => equipmentTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const equipmentTypesI18NRelations = relations(
  equipmentTypesI18N,
  ({ one }) => ({
    equipmentType: one(equipmentTypes, {
      fields: [equipmentTypesI18N.dataId],
      references: [equipmentTypes.id],
    }),
    locale: one(locales, {
      fields: [equipmentTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);
