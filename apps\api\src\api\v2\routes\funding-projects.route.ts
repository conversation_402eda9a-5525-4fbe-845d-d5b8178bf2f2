import { handleEffectError } from '@/api/v2/utils/error-handler';
import { FundingProjectsRuntime } from '@/infrastructure/runtimes/funding-projects.runtime';
import { FundingProjectsServiceLive } from '@/infrastructure/services/funding-projects.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateFundingProjectSchema,
    UpdateFundingProjectSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

const fundingProjectsRoute = new Hono();

fundingProjectsRoute.get('/', async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getAllFundingProjects();
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.get('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getFundingProjectById(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.post('/', effectValidator('json', CreateFundingProjectSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.createFundingProject(body);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.put('/:id', effectValidator('json', UpdateFundingProjectSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.updateFundingProject({ id, ...body });
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.delete('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.deleteFundingProject(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Funding project deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { fundingProjectsRoute };
