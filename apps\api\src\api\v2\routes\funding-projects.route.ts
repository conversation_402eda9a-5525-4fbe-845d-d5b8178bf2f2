import { handleEffectError } from '@/api/v2/utils/error-handler';
import { FundingProjectsRuntime } from '@/infrastructure/runtimes/funding-projects.runtime';
import { FundingProjectsServiceLive } from '@/infrastructure/services/funding-projects.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateFundingProjectSchema,
    FundingProjectSchema,
    ResourceIdSchema,
    UpdateFundingProjectSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllFundingProjectsRoute = describeRoute({
    description: 'Get all Funding Projects',
    operationId: 'getAllFundingProjects',
    responses: {
        200: {
            description: 'List of funding projects',
            content: { 'application/json': { schema: resolver(Schema.Array(FundingProjectSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const getFundingProjectByIdRoute = describeRoute({
    description: 'Get a Funding Project by ID',
    operationId: 'getFundingProjectById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Funding project found',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        404: {
            description: 'Funding project not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const createFundingProjectRoute = describeRoute({
    description: 'Create a Funding Project',
    operationId: 'createFundingProject',
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(CreateFundingProjectSchema) },
        },
    },
    responses: {
        201: {
            description: 'Funding project created',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        400: { description: 'Validation error' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const updateFundingProjectRoute = describeRoute({
    description: 'Update a Funding Project',
    operationId: 'updateFundingProject',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(UpdateFundingProjectSchema) },
        },
    },
    responses: {
        200: {
            description: 'Funding project updated',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        400: { description: 'Validation error' },
        404: { description: 'Funding project not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const deleteFundingProjectRoute = describeRoute({
    description: 'Delete a Funding Project',
    operationId: 'deleteFundingProject',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Funding project deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Funding project not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

const fundingProjectsRoute = new Hono();

fundingProjectsRoute.get('/', getAllFundingProjectsRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getAllFundingProjects();
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.get('/:id', getFundingProjectByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getFundingProjectById(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.post('/', createFundingProjectRoute, effectValidator('json', CreateFundingProjectSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.createFundingProject(body);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.put('/:id', updateFundingProjectRoute, effectValidator('json', UpdateFundingProjectSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.updateFundingProject({ id, ...body });
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.delete('/:id', deleteFundingProjectRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.deleteFundingProject(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Funding project deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { fundingProjectsRoute };
