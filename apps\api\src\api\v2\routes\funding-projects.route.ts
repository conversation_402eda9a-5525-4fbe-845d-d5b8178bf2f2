import { handleEffectError } from '@/api/v2/utils/error-handler';
import { FundingProjectsRuntime } from '@/infrastructure/runtimes/funding-projects.runtime';
import { FundingProjectsServiceLive } from '@/infrastructure/services/funding-projects.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateFundingProjectSchema,
    FundingProjectSchema,
    ResourceIdSchema,
    UpdateFundingProjectSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllFundingProjectsRoute = describeRoute({
    description: 'Get all Funding Projects',
    operationId: 'getAllFundingProjects',
    responses: {
        200: {
            description: 'List of funding projects',
            content: { 'application/json': { schema: resolver(Schema.Array(FundingProjectSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const getFundingProjectByIdRoute = describeRoute({
    description: 'Get a Funding Project by ID',
    operationId: 'getFundingProjectById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Funding project found',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        404: {
            description: 'Funding project not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const createFundingProjectRoute = describeRoute({
    description: 'Create a Funding Project',
    operationId: 'createFundingProject',
    requestBody: {
        required: true,
        content: {
            'application/json': {
                schema: resolver(CreateFundingProjectSchema),
                example: {
                    holderId: "w4ku5n46mdvkv5bjuuml2j3w",
                    typeId: "bqmr7x60br0e6br1rw81uz4j",
                    fciId: "FCI_NEW_001",
                    synchroId: "SYNC_NEW_001",
                    obtainingYear: 2024,
                    endDate: "2026-12-31",
                    translations: [
                        {
                            locale: "en",
                            name: "New AI Research Project",
                            description: "New advanced AI research initiative",
                            otherNames: "New AI Project",
                            acronyms: "NARP"
                        },
                        {
                            locale: "fr",
                            name: "Nouveau Projet de Recherche IA",
                            description: "Nouvelle initiative de recherche avancée en IA",
                            otherNames: "Nouveau Projet IA",
                            acronyms: "NPRI"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        201: {
            description: 'Funding project created',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        400: { description: 'Validation error - Invalid input data or missing required fields' },
        404: { description: 'Foreign key not found - Person or Type does not exist' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const updateFundingProjectRoute = describeRoute({
    description: 'Update a Funding Project',
    operationId: 'updateFundingProject',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Funding Project ID (CUID format)',
            example: 'fp123abc456def789ghi'
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': {
                schema: resolver(UpdateFundingProjectSchema),
                example: {
                    holderId: "w4ku5n46mdvkv5bjuuml2j3w",
                    typeId: "bqmr7x60br0e6br1rw81uz4j",
                    fciId: "FCI_UPDATED_001",
                    synchroId: "SYNC_UPDATED_001",
                    obtainingYear: 2025,
                    endDate: "2027-12-31",
                    translations: [
                        {
                            locale: "en",
                            name: "Updated AI Research Project",
                            description: "Updated advanced AI research initiative",
                            otherNames: "Updated AI Project",
                            acronyms: "UARP"
                        },
                        {
                            locale: "fr",
                            name: "Projet de Recherche IA Mis à Jour",
                            description: "Initiative de recherche avancée en IA mise à jour",
                            otherNames: "Projet IA Mis à Jour",
                            acronyms: "PRIMAJ"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        200: {
            description: 'Funding project updated',
            content: { 'application/json': { schema: resolver(FundingProjectSchema) } },
        },
        400: { description: 'Validation error - Invalid input data' },
        404: { description: 'Funding project not found or Foreign key not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

export const deleteFundingProjectRoute = describeRoute({
    description: 'Delete a Funding Project',
    operationId: 'deleteFundingProject',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Funding project deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Funding project not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Funding Projects'],
});

const fundingProjectsRoute = new Hono();

fundingProjectsRoute.get('/', getAllFundingProjectsRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getAllFundingProjects();
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.get('/:id', getFundingProjectByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.getFundingProjectById(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.post('/', createFundingProjectRoute, effectValidator('json', CreateFundingProjectSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.createFundingProject(body);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.put('/:id', updateFundingProjectRoute, effectValidator('json', UpdateFundingProjectSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.updateFundingProject({ id, ...body });
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

fundingProjectsRoute.delete('/:id', deleteFundingProjectRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* FundingProjectsServiceLive;
        return yield* svc.deleteFundingProject(id);
    });
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Funding project deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { fundingProjectsRoute };
