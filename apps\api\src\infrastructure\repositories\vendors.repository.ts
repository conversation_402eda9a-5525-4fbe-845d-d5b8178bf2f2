import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import type { I18nRow } from '@rie/domain/types';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class VendorsRepositoryLive extends Effect.Service<VendorsRepositoryLive>()(
  'VendorsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllVendors = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.vendors.findMany({
            columns: {
              id: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const findVendorById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.vendors.findFirst({
            where: eq(DBSchema.vendors.id, id),
            columns: {
              id: true,
              startDate: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const createVendor = dbClient.makeQuery(
        (
          execute,
          params: {
            startDate: string;
            endDate: string;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) => {
            return client
              .insert(DBSchema.vendors)
              .values({
                startDate: params.startDate,
                endDate: params.endDate,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.vendors.id,
                startDate: DBSchema.vendors.startDate,
                endDate: DBSchema.vendors.endDate,
                createdAt: DBSchema.vendors.createdAt,
                updatedAt: DBSchema.vendors.updatedAt,
                modifiedBy: DBSchema.vendors.modifiedBy,
              });
          });
        },
      );

      const updateVendor = dbClient.makeQuery(
        (
          execute,
          data: {
            id: string;
            startDate?: string | null;
            endDate?: string | null;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) =>
            client
              .update(DBSchema.vendors)
              .set({
                startDate: data.startDate,
                endDate: data.endDate,
                modifiedBy: data.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.vendors.id, data.id))
              .returning({
                id: DBSchema.vendors.id,
                startDate: DBSchema.vendors.startDate,
                endDate: DBSchema.vendors.endDate,
                createdAt: DBSchema.vendors.createdAt,
                updatedAt: DBSchema.vendors.updatedAt,
                modifiedBy: DBSchema.vendors.modifiedBy,
              }),
          );
        },
      );

      const deleteVendor = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.vendors)
            .where(eq(DBSchema.vendors.id, id))
            .returning({ id: DBSchema.vendors.id }),
        );
      });

      // Method to create vendor with translations
      const createVendorWithTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            vendor: {
              startDate: string;
              endDate: string;
              modifiedBy?: string | null;
            };
            translations: I18nRow[];
          },
        ) => {
          return execute(async (client) => {
            // First create the vendor
            const [vendor] = await client
              .insert(DBSchema.vendors)
              .values({
                startDate: params.vendor.startDate,
                endDate: params.vendor.endDate,
                modifiedBy: params.vendor.modifiedBy,
              })
              .returning({
                id: DBSchema.vendors.id,
                startDate: DBSchema.vendors.startDate,
                endDate: DBSchema.vendors.endDate,
                createdAt: DBSchema.vendors.createdAt,
                updatedAt: DBSchema.vendors.updatedAt,
                modifiedBy: DBSchema.vendors.modifiedBy,
              });

            // Then create the translations if provided
            if (params.translations.length > 0) {
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: vendor.id,
                  locale: translation.locale,
                  name: translation.name,
                  website: translation.website,
                  description: translation.description,
                  otherNames: translation.otherNames,
                }),
              );

              await client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert);
            }

            // Return vendor with translations
            return await client.query.vendors.findFirst({
              where: eq(DBSchema.vendors.id, vendor.id),
              columns: {
                id: true,
                startDate: true,
                endDate: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    website: true,
                    description: true,
                    otherNames: true,
                  },
                },
              },
            });
          });
        },
      );

      // Method to update vendor translations
      const updateVendorTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            vendorId: string;
            translations: I18nRow[];
          },
        ) => {
          return execute(async (client) => {
            // Delete existing translations
            await client
              .delete(DBSchema.vendorsI18N)
              .where(eq(DBSchema.vendorsI18N.dataId, params.vendorId));

            // Insert new translations
            if (params.translations.length > 0) {
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: params.vendorId,
                  locale: translation.locale,
                  name: translation.name,
                  website: translation.website,
                  description: translation.description,
                  otherNames: translation.otherNames,
                }),
              );

              return await client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert)
                .returning({
                  iid: DBSchema.vendorsI18N.id,
                  dataId: DBSchema.vendorsI18N.dataId,
                  locale: DBSchema.vendorsI18N.locale,
                  name: DBSchema.vendorsI18N.name,
                  website: DBSchema.vendorsI18N.website,
                  description: DBSchema.vendorsI18N.description,
                  otherNames: DBSchema.vendorsI18N.otherNames,
                });
            }

            return [];
          });
        },
      );

      // Method to update vendor with translations
      const updateVendorWithTranslations = dbClient.makeQuery(
        (
          execute,
          params: {
            vendorId: string;
            vendor: {
              startDate?: string | null;
              endDate?: string | null;
              modifiedBy?: string | null;
            };
            translations: I18nRow[];
          },
        ) => {
          return execute(async (client) => {
            // First update the vendor
            await client
              .update(DBSchema.vendors)
              .set({
                startDate: params.vendor.startDate,
                endDate: params.vendor.endDate,
                modifiedBy: params.vendor.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.vendors.id, params.vendorId))
              .returning({
                id: DBSchema.vendors.id,
                startDate: DBSchema.vendors.startDate,
                endDate: DBSchema.vendors.endDate,
                createdAt: DBSchema.vendors.createdAt,
                updatedAt: DBSchema.vendors.updatedAt,
                modifiedBy: DBSchema.vendors.modifiedBy,
              });

            // Delete existing translations
            await client
              .delete(DBSchema.vendorsI18N)
              .where(eq(DBSchema.vendorsI18N.dataId, params.vendorId));

            // Insert new translations
            if (params.translations.length > 0) {
              const translationsToInsert = params.translations.map(
                (translation) => ({
                  dataId: params.vendorId,
                  locale: translation.locale,
                  name: translation.name,
                  website: translation.website,
                  description: translation.description,
                  otherNames: translation.otherNames,
                }),
              );

              await client
                .insert(DBSchema.vendorsI18N)
                .values(translationsToInsert);
            }

            // Return vendor with translations
            return await client.query.vendors.findFirst({
              where: eq(DBSchema.vendors.id, params.vendorId),
              columns: {
                id: true,
                startDate: true,
                endDate: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    website: true,
                    description: true,
                    otherNames: true,
                  },
                },
              },
            });
          });
        },
      );

      return {
        findAllVendors,
        findVendorById,
        createVendor,
        updateVendor,
        deleteVendor,
        createVendorWithTranslations,
        updateVendorTranslations,
        updateVendorWithTranslations,
      } as const;
    }),
  },
) {}
