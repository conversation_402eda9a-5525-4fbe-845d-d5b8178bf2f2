import * as Schema from 'effect/Schema';

export const PeopleId = Schema.String.pipe(Schema.brand('PeopleId'));
export type PeopleId = typeof PeopleId.Type;

export const PeopleRawSchema = Schema.Struct({
  id: Schema.NonEmptyString,
  familyName: Schema.NonEmptyString,
  givenName: Schema.NonEmptyString,
  personEmails: Schema.Array(Schema.NonEmptyString),
});

export const PeopleListViewSchema = Schema.Struct({
  id: PeopleId,
  familyName: Schema.NonEmptyString,
  givenName: Schema.NonEmptyString,
  emails: Schema.Array(Schema.NonEmptyString),
});

// — Full Person shape (people table doesn't have translations)
export const PersonSchema = Schema.Struct({
  id: Schema.String, // cuid
  guidId: Schema.optional(Schema.String), // nullable - references guids
  uid: Schema.optional(Schema.String), // nullable
  firstName: Schema.String, // required
  lastName: Schema.String, // required
  userId: Schema.optional(Schema.String), // nullable - references users
  createdAt: Schema.String, // ISO timestamp
  updatedAt: Schema.String, // ISO timestamp
  modifiedBy: Schema.optional(Schema.String), // nullable
});

export type Person = Schema.Schema.Type<typeof PersonSchema>;

// — Input schemas for API
export const CreatePersonSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  uid: Schema.optional(Schema.String),
  firstName: Schema.String,
  lastName: Schema.String,
  userId: Schema.optional(Schema.String),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreatePersonPayload = Schema.Schema.Type<typeof CreatePersonSchema>;

export const UpdatePersonSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  uid: Schema.optional(Schema.String),
  firstName: Schema.optional(Schema.String),
  lastName: Schema.optional(Schema.String),
  userId: Schema.optional(Schema.String),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdatePersonPayload = Schema.Schema.Type<
  typeof UpdatePersonSchema
> & { id: string };

// — Database schema (for serializers)
export const DbPersonSchema = PersonSchema;

// — Item schema (for serializers)
export const PersonItemSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

export type PersonItem = Schema.Schema.Type<typeof PersonItemSchema>;

// — Response schemas
export const PersonResponseSchema = PersonSchema;
export const PersonListResponseSchema = Schema.Array(PersonResponseSchema);

export type PersonResponse = Schema.Schema.Type<typeof PersonResponseSchema>;
export type PersonListResponse = Schema.Schema.Type<
  typeof PersonListResponseSchema
>;
