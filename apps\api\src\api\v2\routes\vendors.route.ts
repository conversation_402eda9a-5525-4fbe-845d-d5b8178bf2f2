import { effectValidator } from '@hono/effect-validator';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { handleEffectError } from '@/api/v2/utils/error-handler';
import { VendorsRuntime } from '@/infrastructure/runtimes/vendors.runtime';
import { VendorsServiceLive } from '@/infrastructure/services/vendors.service';
import {
  CreateVendorSchema,
  ResourceIdSchema,
  UpdateVendorSchema,
  VendorResponseSchema,
} from '@rie/domain/schemas';

export const createVendorRoute = describeRoute({
  description: 'Crée un bâtiment',
  operationId: 'createVendor',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CreateVendorSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Bâtiment créé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const updateVendorRoute = describeRoute({
  description: 'Mettre à jour un bâtiment',
  operationId: 'updateVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du bâtiment à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UpdateVendorSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Bâtiment mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const deleteVendorRoute = describeRoute({
  description: 'Supprime un bâtiment',
  operationId: 'deleteVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du bâtiment à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment supprimé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const getAllVendorsRoute = describeRoute({
  description: 'Obtient tous les bâtiments',
  operationId: 'getAllVendors',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(VendorResponseSchema)),
        },
      },
      description: 'Bâtiments retournés avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const getVendorByIdRoute = describeRoute({
  description: 'Obtient un fournisseur par ID',
  operationId: 'getVendorById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du fournisseur à récupérer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Fournisseur retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Fournisseur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

const vendorsRoute = new Hono();

vendorsRoute.get('/', getAllVendorsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const svc = yield* VendorsServiceLive;
    return yield* svc.getAllVendors();
  });

  const vendors = await VendorsRuntime.runPromise(program);

  return ctx.json(vendors);
});

vendorsRoute.post(
  '/',
  createVendorRoute,
  effectValidator('json', CreateVendorSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const svc = yield* VendorsServiceLive;
      return yield* svc.createVendor(body);
    });

    const maybeVendor = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybeVendor);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybeVendor)) {
      return ctx.json(maybeVendor.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.put(
  '/:id',
  updateVendorRoute,
  effectValidator('json', UpdateVendorSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const svc = yield* VendorsServiceLive;
      return yield* svc.updateVendor({ id, ...body });
    });

    const maybeVendor = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybeVendor);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybeVendor)) {
      return ctx.json(maybeVendor.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.delete('/:id', deleteVendorRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const svc = yield* VendorsServiceLive;
    return yield* svc.deleteVendor(id);
  });

  const result = await VendorsRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  return ctx.json({
    success: true,
    message: 'Vendor deleted successfully',
  });
});

vendorsRoute.get('/:id', getVendorByIdRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const svc = yield* VendorsServiceLive;
    return yield* svc.getVendorById(id);
  });

  const maybeVendor = await VendorsRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, maybeVendor);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(maybeVendor)) {
    return ctx.json(maybeVendor.value);
  }

  return ctx.json({ error: 'Vendor not found' }, 404);
});

export { vendorsRoute };
