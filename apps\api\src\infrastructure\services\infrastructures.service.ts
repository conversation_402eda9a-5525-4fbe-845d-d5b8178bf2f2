import { DatabaseLive } from '@/infrastructure/database/database.live';
import { CreateInfrastructureSchema, InfrastructureSchema, UpdateInfrastructureSchema } from '@rie/domain/schemas';
import * as DbSchema from '@rie/db-schema';
import { and, eq } from 'drizzle-orm';
import * as Context from 'effect/Context';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as Schema from 'effect/Schema';

export interface InfrastructuresService {
  getAllInfrastructures: () => Effect.Effect<
    Array<Schema.Schema.Type<typeof InfrastructureSchema>>,
    Error
  >;
  getInfrastructureById: (
    id: string,
  ) => Effect.Effect<Schema.Schema.Type<typeof InfrastructureSchema>, Error>;
  createInfrastructure: (
    data: Schema.Schema.Type<typeof CreateInfrastructureSchema>,
  ) => Effect.Effect<Schema.Schema.Type<typeof InfrastructureSchema>, Error>;
  updateInfrastructure: (
    data: { id: string } & Schema.Schema.Type<typeof UpdateInfrastructureSchema>,
  ) => Effect.Effect<Schema.Schema.Type<typeof InfrastructureSchema>, Error>;
  deleteInfrastructure: (id: string) => Effect.Effect<void, Error>;
}

export const InfrastructuresService = Context.GenericTag<InfrastructuresService>(
  '@services/InfrastructuresService',
);

export const InfrastructuresServiceLive = Layer.effect(
  InfrastructuresService,
  Effect.gen(function* () {
    const db = yield* DatabaseLive;

    const getAllInfrastructures = () =>
      Effect.gen(function* () {
        const infrastructures = yield* Effect.tryPromise({
          try: () =>
            db
              .select({
                id: DbSchema.infrastructures.id,
                guidId: DbSchema.infrastructures.guidId,
                typeId: DbSchema.infrastructures.typeId,
                addressId: DbSchema.infrastructures.addressId,
                statusId: DbSchema.infrastructures.statusId,
                website: DbSchema.infrastructures.website,
                is_featured: DbSchema.infrastructures.is_featured,
                visibilityId: DbSchema.infrastructures.visibilityId,
                createdAt: DbSchema.infrastructures.createdAt,
                updatedAt: DbSchema.infrastructures.updatedAt,
                modifiedBy: DbSchema.infrastructures.modifiedBy,
                translations: {
                  locale: DbSchema.infrastructuresI18N.locale,
                  name: DbSchema.infrastructuresI18N.name,
                  description: DbSchema.infrastructuresI18N.description,
                  otherNames: DbSchema.infrastructuresI18N.otherNames,
                  acronyms: DbSchema.infrastructuresI18N.acronyms,
                },
              })
              .from(DbSchema.infrastructures)
              .leftJoin(
                DbSchema.infrastructuresI18N,
                eq(DbSchema.infrastructures.id, DbSchema.infrastructuresI18N.dataId),
              ),
          catch: (error) => new Error(`Failed to fetch infrastructures: ${error}`),
        });

        // Group translations by infrastructure
        const groupedInfrastructures = infrastructures.reduce(
          (acc, row) => {
            const existing = acc.find((item) => item.id === row.id);
            if (existing) {
              if (row.translations.locale) {
                existing.translations.push(row.translations);
              }
            } else {
              acc.push({
                ...row,
                translations: row.translations.locale ? [row.translations] : [],
              });
            }
            return acc;
          },
          [] as Array<Schema.Schema.Type<typeof InfrastructureSchema>>,
        );

        return groupedInfrastructures;
      });

    const getInfrastructureById = (id: string) =>
      Effect.gen(function* () {
        const infrastructure = yield* Effect.tryPromise({
          try: () =>
            db
              .select({
                id: DbSchema.infrastructures.id,
                guidId: DbSchema.infrastructures.guidId,
                typeId: DbSchema.infrastructures.typeId,
                addressId: DbSchema.infrastructures.addressId,
                statusId: DbSchema.infrastructures.statusId,
                website: DbSchema.infrastructures.website,
                is_featured: DbSchema.infrastructures.is_featured,
                visibilityId: DbSchema.infrastructures.visibilityId,
                createdAt: DbSchema.infrastructures.createdAt,
                updatedAt: DbSchema.infrastructures.updatedAt,
                modifiedBy: DbSchema.infrastructures.modifiedBy,
                translations: {
                  locale: DbSchema.infrastructuresI18N.locale,
                  name: DbSchema.infrastructuresI18N.name,
                  description: DbSchema.infrastructuresI18N.description,
                  otherNames: DbSchema.infrastructuresI18N.otherNames,
                  acronyms: DbSchema.infrastructuresI18N.acronyms,
                },
              })
              .from(DbSchema.infrastructures)
              .leftJoin(
                DbSchema.infrastructuresI18N,
                eq(DbSchema.infrastructures.id, DbSchema.infrastructuresI18N.dataId),
              )
              .where(eq(DbSchema.infrastructures.id, id)),
          catch: (error) => new Error(`Failed to fetch infrastructure: ${error}`),
        });

        if (infrastructure.length === 0) {
          return yield* Effect.fail(new Error('Infrastructure not found'));
        }

        // Group translations
        const result = infrastructure.reduce(
          (acc, row) => {
            if (!acc) {
              acc = {
                ...row,
                translations: row.translations.locale ? [row.translations] : [],
              };
            } else if (row.translations.locale) {
              acc.translations.push(row.translations);
            }
            return acc;
          },
          null as Schema.Schema.Type<typeof InfrastructureSchema> | null,
        );

        return result!;
      });

    const createInfrastructure = (
      data: Schema.Schema.Type<typeof CreateInfrastructureSchema>,
    ) =>
      Effect.gen(function* () {
        const result = yield* Effect.tryPromise({
          try: () =>
            db.transaction(async (tx) => {
              // Insert infrastructure
              const [infrastructure] = await tx
                .insert(DbSchema.infrastructures)
                .values({
                  guidId: data.guidId,
                  typeId: data.typeId,
                  addressId: data.addressId,
                  statusId: data.statusId,
                  website: data.website,
                  is_featured: data.is_featured,
                  visibilityId: data.visibilityId,
                })
                .returning();

              // Insert translations
              if (data.translations.length > 0) {
                await tx.insert(DbSchema.infrastructuresI18N).values(
                  data.translations.map((translation) => ({
                    dataId: infrastructure.id,
                    locale: translation.locale,
                    name: translation.name,
                    description: translation.description,
                    otherNames: translation.otherNames,
                    acronyms: translation.acronyms,
                  })),
                );
              }

              return { ...infrastructure, translations: data.translations };
            }),
          catch: (error) => new Error(`Failed to create infrastructure: ${error}`),
        });

        return result;
      });

    const updateInfrastructure = (
      data: { id: string } & Schema.Schema.Type<typeof UpdateInfrastructureSchema>,
    ) =>
      Effect.gen(function* () {
        const result = yield* Effect.tryPromise({
          try: () =>
            db.transaction(async (tx) => {
              // Update infrastructure
              const [infrastructure] = await tx
                .update(DbSchema.infrastructures)
                .set({
                  guidId: data.guidId,
                  typeId: data.typeId,
                  addressId: data.addressId,
                  statusId: data.statusId,
                  website: data.website,
                  is_featured: data.is_featured,
                  visibilityId: data.visibilityId,
                  updatedAt: new Date().toISOString(),
                })
                .where(eq(DbSchema.infrastructures.id, data.id))
                .returning();

              if (!infrastructure) {
                throw new Error('Infrastructure not found');
              }

              // Update translations if provided
              if (data.translations) {
                // Delete existing translations
                await tx
                  .delete(DbSchema.infrastructuresI18N)
                  .where(eq(DbSchema.infrastructuresI18N.dataId, data.id));

                // Insert new translations
                if (data.translations.length > 0) {
                  await tx.insert(DbSchema.infrastructuresI18N).values(
                    data.translations.map((translation) => ({
                      dataId: infrastructure.id,
                      locale: translation.locale,
                      name: translation.name,
                      description: translation.description,
                      otherNames: translation.otherNames,
                      acronyms: translation.acronyms,
                    })),
                  );
                }
              }

              return { ...infrastructure, translations: data.translations || [] };
            }),
          catch: (error) => new Error(`Failed to update infrastructure: ${error}`),
        });

        return result;
      });

    const deleteInfrastructure = (id: string) =>
      Effect.gen(function* () {
        yield* Effect.tryPromise({
          try: () =>
            db.transaction(async (tx) => {
              // Delete translations first
              await tx
                .delete(DbSchema.infrastructuresI18N)
                .where(eq(DbSchema.infrastructuresI18N.dataId, id));

              // Delete infrastructure
              const result = await tx
                .delete(DbSchema.infrastructures)
                .where(eq(DbSchema.infrastructures.id, id))
                .returning();

              if (result.length === 0) {
                throw new Error('Infrastructure not found');
              }
            }),
          catch: (error) => new Error(`Failed to delete infrastructure: ${error}`),
        });
      });

    return {
      getAllInfrastructures,
      getInfrastructureById,
      createInfrastructure,
      updateInfrastructure,
      deleteInfrastructure,
    } satisfies InfrastructuresService;
  }),
).pipe(Layer.provide(DatabaseLive));
