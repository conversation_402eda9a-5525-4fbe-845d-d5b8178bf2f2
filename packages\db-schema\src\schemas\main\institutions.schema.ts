import {
  campuses,
  equipments,
  guids,
  locales,
  people,
  units,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const institutions = pgTable('institutions', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  guidId: text().references(() => guids.id, {
    onDelete: 'cascade',
    onUpdate: 'cascade',
  }),
  typeId: text()
    .notNull()
    .references(() => institutionTypes.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const institutionsRelations = relations(
  institutions,
  ({ one, many }) => ({
    type: one(institutionTypes, {
      fields: [institutions.typeId],
      references: [institutionTypes.id],
    }),
    guid: one(guids, {
      fields: [institutions.guidId],
      references: [guids.id],
    }),
    associatedPeople: many(institutionAssociatedPeople),
    campuses: many(campuses),
    translations: many(institutionsI18N),
    associatedEquipment: many(equipments),
    associatedUnits: many(institutionAssociatedUnits),
  }),
);

export const institutionsI18N = pgTable(
  'institutions_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => institutions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    acronyms: text(),
    otherNames: text(),
  },
  (table) => [
    {
      dataIdUnique: unique().on(table.dataId, table.locale),
      localeIdUnique: unique().on(table.locale, table.name),
    },
  ],
);

export const institutionsI18NRelations = relations(
  institutionsI18N,
  ({ one }) => ({
    institution: one(institutions, {
      fields: [institutionsI18N.dataId],
      references: [institutions.id],
    }),
    locale: one(locales, {
      fields: [institutionsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const institutionAssociatedPeople = pgTable(
  'institution_associated_people',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    institutionId: text()
      .notNull()
      .references(() => institutions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    roleTypeId: text()
      .notNull()
      .references(() => institutionRoleTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      uniqueInstitutionAffiliation: unique().on(
        table.institutionId,
        table.personId,
        table.roleTypeId,
      ),
    },
  ],
);

export const institutionAssociatedPeopleRelations = relations(
  institutionAssociatedPeople,
  ({ one }) => ({
    institution: one(institutions, {
      fields: [institutionAssociatedPeople.institutionId],
      references: [institutions.id],
    }),
    person: one(people, {
      fields: [institutionAssociatedPeople.personId],
      references: [people.id],
    }),
    roleType: one(institutionRoleTypes, {
      fields: [institutionAssociatedPeople.roleTypeId],
      references: [institutionRoleTypes.id],
    }),
  }),
);

export const institutionTypes = pgTable(
  'institution_types',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    uid: text(),
  },
  (table) => [
    {
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const institutionTypesRelations = relations(
  institutionTypes,
  ({ many }) => ({
    institutions: many(institutions),
    translations: many(institutionTypesI18N),
  }),
);

export const institutionTypesI18N = pgTable(
  'institution_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => institutionTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const institutionTypesI18NRelations = relations(
  institutionTypesI18N,
  ({ one }) => ({
    institutionType: one(institutionTypes, {
      fields: [institutionTypesI18N.dataId],
      references: [institutionTypes.id],
    }),
    locale: one(locales, {
      fields: [institutionTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const institutionRoleTypes = pgTable(
  'institution_role_types',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    uid: text(),
  },
  (table) => [
    {
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const institutionRoleTypesRelations = relations(
  institutionRoleTypes,
  ({ many }) => ({
    affiliations: many(institutionAssociatedPeople),
    translations: many(institutionRoleTypesI18N),
  }),
);

export const institutionRoleTypesI18N = pgTable(
  'institution_role_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => institutionRoleTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const institutionRoleTypesI18NRelations = relations(
  institutionRoleTypesI18N,
  ({ one }) => ({
    institutionRoleType: one(institutionRoleTypes, {
      fields: [institutionRoleTypesI18N.dataId],
      references: [institutionRoleTypes.id],
    }),
    locale: one(locales, {
      fields: [institutionRoleTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const institutionAssociatedUnits = pgTable(
  'institution_associated_units',
  {
    institutionId: text()
      .notNull()
      .references(() => institutions.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    unitId: text()
      .notNull()
      .references(() => units.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      institutionUnitPk: primaryKey({
        columns: [table.institutionId, table.unitId],
      }),
    },
  ],
);

export const institutionAssociatedUnitsRelations = relations(
  institutionAssociatedUnits,
  ({ one }) => ({
    institution: one(institutions, {
      fields: [institutionAssociatedUnits.institutionId],
      references: [institutions.id],
    }),
    unit: one(units, {
      fields: [institutionAssociatedUnits.unitId],
      references: [units.id],
    }),
  }),
);
