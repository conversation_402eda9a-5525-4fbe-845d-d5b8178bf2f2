import { equipmentAssociatedTechniques, locales, users } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const techniques = pgTable('techniques', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const techniquesRelations = relations(techniques, ({ many }) => ({
  translations: many(techniquesI18N),
  equipments: many(equipmentAssociatedTechniques),
}));

export const techniquesI18N = pgTable(
  'techniques_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => techniques.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const techniquesI18NRelations = relations(techniquesI18N, ({ one }) => ({
  excellenceHubs: one(techniques, {
    fields: [techniquesI18N.dataId],
    references: [techniques.id],
  }),
  locale: one(locales, {
    fields: [techniquesI18N.locale],
    references: [locales.code],
  }),
}));
