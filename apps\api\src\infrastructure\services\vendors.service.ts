import { VendorsRepositoryLive } from '@/infrastructure/repositories/vendors.repository';
import {
  VendorAlreadyExistsError,
  VendorNotFoundError,
} from '@rie/domain/errors';
import type {
  CreateVendorPayload,
  UpdateVendorPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class VendorsServiceLive extends Effect.Service<VendorsServiceLive>()(
  'VendorsServiceLive',
  {
    dependencies: [VendorsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllVendors = () => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const vendorsRepository = yield* VendorsRepositoryLive;
          const vendors = yield* vendorsRepository.findAllVendors();
          const t1 = performance.now();

          console.log(`Call to getAllVendors took ${t1 - t0} milliseconds.`);

          return vendors;
        });
      };

      const getVendorById = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const vendorsRepository = yield* VendorsRepositoryLive;
          const vendor = yield* vendorsRepository.findVendorById(id);

          if (!vendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }

          const t1 = performance.now();
          console.log(`Call to getVendorById took ${t1 - t0} milliseconds.`); return vendor;
        });
      };

      const createVendor = (data: CreateVendorPayload) => {
        return Effect.gen(function* () {
          const vendorsRepository = yield* VendorsRepositoryLive; return yield* vendorsRepository.createVendor(data).pipe(
            Effect.catchTag('DatabaseError', (error) => {
              if (error.type === 'unique_violation') {
                return Effect.fail(
                  new VendorAlreadyExistsError({
                    name: data.translations?.[0]?.name || 'unknown',
                  }),
                );
              }

              return Effect.fail(
                new Error(`Unexpected database error: ${error.message}`),
              );
            }),
          );
        });
      };

      const updateVendor = (params: UpdateVendorPayload) => {
        const t0 = performance.now();
        const { id, ...updateData } = params;

        return Effect.gen(function* () {
          const vendorsRepository = yield* VendorsRepositoryLive;

          const [vendor] = yield* vendorsRepository.updateVendor({
            id,
            ...updateData,
          });
          const t1 = performance.now();

          console.log(`Call to updateVendor took ${t1 - t0} milliseconds.`);

          return vendor;
        });
      };

      const deleteVendor = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const vendorsRepository = yield* VendorsRepositoryLive;

          const existingVendor = yield* vendorsRepository.findVendorById(id);

          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }

          const result = yield* vendorsRepository.deleteVendor(id);
          const t1 = performance.now();

          console.log(`Call to deleteVendor took ${t1 - t0} milliseconds.`);

          return result.length > 0;
        });
      };

      return {
        getAllVendors,
        getVendorById,
        createVendor,
        updateVendor,
        deleteVendor,
      } as const;
    }),
  },
) { }
