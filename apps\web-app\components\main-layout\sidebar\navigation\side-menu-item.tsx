'use client';

import { Link } from '@/lib/navigation';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { useSelectedLayoutSegment } from 'next/navigation';
import type { ComponentProps, PropsWithChildren } from 'react';

const sideMenuItemVariants = cva('flex items-center hover:bg-accent', {
  defaultVariants: {
    variant: 'default',
  },
  variants: {
    variant: {
      default: 'hover:text-primary gap-3 rounded-lg px-3 py-2 transition-all',
      mobile: 'gap-2 text-lg font-semibold',
    },
  },
});

type SideMenuItemProps = {
  className?: string;
} & ComponentProps<typeof Link> &
  VariantProps<typeof sideMenuItemVariants>;

export const SideMenuItem = ({
  children,
  className,
  href,
  variant,
}: PropsWithChildren<SideMenuItemProps>) => {
  const selectedLayoutSegment = useSelectedLayoutSegment();
  const pathname = selectedLayoutSegment ? `/${selectedLayoutSegment}` : '/';
  const isActive = pathname === href;

  return (
    <Link
      aria-current={isActive ? 'page' : undefined}
      className={cn(
        sideMenuItemVariants({ className, variant }),
        isActive ? 'text-primary' : 'text-foreground',
      )}
      href={href}
    >
      {children}
    </Link>
  );
};
