import type {
  DbPermissionAction,
  DbPermissionDomain,
} from '@rie/db-schema/entity-types';
import * as Data from 'effect/Data';

/**
 * Error thrown when attempting to create a permission that already exists
 */
export class PermissionAlreadyExistsError extends Data.TaggedError(
  'PermissionAlreadyExistsError',
)<{
  readonly domain: DbPermissionDomain;
  readonly action: DbPermissionAction;
}> {}

/**
 * Error thrown when attempting to create a permission that violates
 */
export class ForeignKeyViolationError extends Data.TaggedError(
  'ForeignKeyViolationError',
)<{
  readonly domain: DbPermissionDomain;
  readonly action: DbPermissionAction;
}> {}

/**
 * Error thrown when a permission cannot be found
 */
export class PermissionNotFoundError extends Data.TaggedError(
  'PermissionNotFoundError',
)<{
  readonly id?: string;
  readonly domain?: DbPermissionDomain;
  readonly action?: DbPermissionAction;
}> {}

/**
 * Error thrown when permission validation fails
 */
export class PermissionValidationError extends Data.TaggedError(
  'PermissionValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> {}

export class RoleNotFoundError extends Data.TaggedError('RoleNotFoundError')<{
  readonly id: string;
}> {}

export class RoleAlreadyExistsError extends Data.TaggedError(
  'RoleAlreadyExistsError',
)<{
  readonly name: string;
}> {}

export class PermissionGroupNotFoundError extends Data.TaggedError(
  'PermissionGroupNotFoundError',
)<{
  readonly id: string;
}> {}

export class PermissionGroupAlreadyExistsError extends Data.TaggedError(
  'PermissionGroupAlreadyExistsError',
)<{
  readonly name: string;
}> {}
