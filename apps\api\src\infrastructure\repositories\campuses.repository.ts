import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class CampusesRepositoryLive extends Effect.Service<CampusesRepositoryLive>()(
  'CampusesRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      const findAll = db.makeQuery((exec) =>
        exec((client) =>
          client.query.campuses.findMany({
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
            },
          }),
        ),
      );

      const findById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.campuses.findFirst({
            where: eq(DBSchema.campuses.id, id),
            columns: {
              id: true,
              sadId: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: { id: true, locale: true, name: true },
              },
            },
          }),
        ),
      );

      const createOne = db.makeQuery(
        (
          exec,
          params: {
            sadId?: string | null;
            institutionId: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.campuses)
              .values({
                sadId: params.sadId,
                institutionId: params.institutionId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.campuses.id,
                sadId: DBSchema.campuses.sadId,
                institutionId: DBSchema.campuses.institutionId,
                createdAt: DBSchema.campuses.createdAt,
                updatedAt: DBSchema.campuses.updatedAt,
                modifiedBy: DBSchema.campuses.modifiedBy,
              }),
          ),
      );

      const updateOne = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            sadId?: string | null;
            institutionId?: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.campuses)
              .set({
                sadId: params.sadId,
                institutionId: params.institutionId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.campuses.id, params.id))
              .returning({
                id: DBSchema.campuses.id,
                sadId: DBSchema.campuses.sadId,
                institutionId: DBSchema.campuses.institutionId,
                createdAt: DBSchema.campuses.createdAt,
                updatedAt: DBSchema.campuses.updatedAt,
                modifiedBy: DBSchema.campuses.modifiedBy,
              }),
          ),
      );

      const deleteOne = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.campuses)
            .where(eq(DBSchema.campuses.id, id))
            .returning({ id: DBSchema.campuses.id }),
        ),
      );

      const createWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            campus: {
              sadId?: string | null;
              institutionId: string;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            const [campus] = await client
              .insert(DBSchema.campuses)
              .values({
                sadId: params.campus.sadId,
                institutionId: params.campus.institutionId,
                modifiedBy: params.campus.modifiedBy,
              })
              .returning({ id: DBSchema.campuses.id });

            if (params.translations.length) {
              await client.insert(DBSchema.campusesI18N).values(
                params.translations.map((t) => ({
                  dataId: campus.id,
                  locale: t.locale,
                  name: t.name,
                })),
              );
            }

            return campus;
          }),
      );

      const updateTranslations = db.makeQuery(
        (
          exec,
          params: {
            campusId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            await client
              .delete(DBSchema.campusesI18N)
              .where(eq(DBSchema.campusesI18N.dataId, params.campusId));

            if (params.translations.length) {
              return await client
                .insert(DBSchema.campusesI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.campusId,
                    locale: t.locale,
                    name: t.name,
                  })),
                )
                .returning({
                  id: DBSchema.campusesI18N.id,
                  dataId: DBSchema.campusesI18N.dataId,
                  locale: DBSchema.campusesI18N.locale,
                  name: DBSchema.campusesI18N.name,
                });
            }
            return [];
          }),
      );

      // Method to update campus with translations
      const updateCampusWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            campusId: string;
            campus: {
              sadId?: string | null;
              institutionId?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            // First update the campus
            const [updatedCampus] = await client
              .update(DBSchema.campuses)
              .set({
                sadId: params.campus.sadId,
                institutionId: params.campus.institutionId,
                modifiedBy: params.campus.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.campuses.id, params.campusId))
              .returning({
                id: DBSchema.campuses.id,
                sadId: DBSchema.campuses.sadId,
                institutionId: DBSchema.campuses.institutionId,
                createdAt: DBSchema.campuses.createdAt,
                updatedAt: DBSchema.campuses.updatedAt,
                modifiedBy: DBSchema.campuses.modifiedBy,
              });

            // Delete existing translations
            await client
              .delete(DBSchema.campusesI18N)
              .where(eq(DBSchema.campusesI18N.dataId, params.campusId));

            // Insert new translations
            if (params.translations.length > 0) {
              await client.insert(DBSchema.campusesI18N).values(
                params.translations.map((t) => ({
                  dataId: params.campusId,
                  locale: t.locale,
                  name: t.name,
                })),
              );
            }

            // Return campus with translations
            return await client.query.campuses.findFirst({
              where: eq(DBSchema.campuses.id, params.campusId),
              columns: {
                id: true,
                sadId: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                  },
                },
              },
            });
          }),
      );

      return {
        findAll,
        findById,
        createOne,
        updateOne,
        deleteOne,
        createWithTranslations,
        updateTranslations,
        updateCampusWithTranslations,
      } as const;
    }),
  },
) { }
