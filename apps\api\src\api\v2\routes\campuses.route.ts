import { handleEffectError } from '@/api/v2/utils/error-handler';
import { CampusesRuntime } from '@/infrastructure/runtimes/campuses.runtime';
import { CampusesServiceLive } from '@/infrastructure/services/campuses.service';
import {
    CampusResponseSchema,
    CreateCampusSchema,
    ResourceIdSchema,
    UpdateCampusSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

export const createCampusRoute = describeRoute({
    description: 'Create a Campus',
    operationId: 'createCampus',
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(CreateCampusSchema) },
        },
    },
    responses: {
        201: {
            description: 'Campus created',
            content: { 'application/json': { schema: resolver(CampusResponseSchema) } },
        },
        400: { /* validation error */ },
        404: { /* FK not found */ },
        500: { /* generic */ },
    },
    tags: ['Campuses'],
});

export const updateCampusRoute = describeRoute({
    description: 'Update a Campus',
    operationId: 'updateCampus',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(UpdateCampusSchema) },
        },
    },
    responses: {
        200: {
            description: 'Campus updated',
            content: { 'application/json': { schema: resolver(CampusResponseSchema) } },
        },
        400: {},
        404: {},
        500: {},
    },
    tags: ['Campuses'],
});

export const deleteCampusRoute = describeRoute({
    description: 'Delete a Campus',
    operationId: 'deleteCampus',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Campus deleted',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
                    ),
                },
            },
        },
        404: {},
        500: {},
    },
    tags: ['Campuses'],
});

export const getCampusByIdRoute = describeRoute({
    description: 'Get a Campus by ID',
    operationId: 'getCampusById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Campus found',
            content: { 'application/json': { schema: resolver(CampusResponseSchema) } },
        },
        404: {
            description: 'Campus not found',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ error: Schema.String }),
                    ),
                },
            },
        },
        500: {
            description: 'Internal server error',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ error: Schema.String }),
                    ),
                },
            },
        },
    },
    tags: ['Campuses'],
});

export const getAllCampusesRoute = describeRoute({
    description: 'List all Campuses',
    operationId: 'getAllCampuses',
    responses: {
        200: {
            description: 'Campuses returned',
            content: {
                'application/json': { schema: resolver(Schema.Array(CampusResponseSchema)) },
            },
        },
        500: {},
    },
    tags: ['Campuses'],
});

const campusesRoute = new Hono();

campusesRoute.get(
    '/',
    getAllCampusesRoute,
    async (ctx) => {
        const program = Effect.gen(function* () {
            const svc = yield* CampusesServiceLive;
            return yield* svc.getAllCampuses();
        });
        const result = await CampusesRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

campusesRoute.get(
    '/:id',
    getCampusByIdRoute,
    async (ctx) => {
        const id = ctx.req.param('id');
        const program = Effect.gen(function* () {
            const svc = yield* CampusesServiceLive;
            return yield* svc.getCampusById(id);
        });
        const result = await CampusesRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

campusesRoute.post(
    '/',
    createCampusRoute,
    effectValidator('json', CreateCampusSchema),
    async (ctx) => {
        const body = ctx.req.valid('json');
        const program = Effect.gen(function* () {
            const svc = yield* CampusesServiceLive;
            return yield* svc.createCampus(body);
        });
        const result = await CampusesRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value, 201);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

campusesRoute.put(
    '/:id',
    updateCampusRoute,
    effectValidator('json', UpdateCampusSchema),
    async (ctx) => {
        const id = ctx.req.param('id');
        const body = ctx.req.valid('json');
        const program = Effect.gen(function* () {
            const svc = yield* CampusesServiceLive;
            return yield* svc.updateCampus({ id, ...body });
        });
        const result = await CampusesRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

campusesRoute.delete(
    '/:id',
    deleteCampusRoute,
    async (ctx) => {
        const id = ctx.req.param('id');
        const program = Effect.gen(function* () {
            const svc = yield* CampusesServiceLive;
            return yield* svc.deleteCampus(id);
        });
        const result = await CampusesRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json({ success: true, message: 'Campus deleted' });
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

export { campusesRoute };
