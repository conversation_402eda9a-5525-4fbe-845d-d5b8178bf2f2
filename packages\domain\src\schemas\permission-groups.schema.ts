import * as Schema from 'effect/Schema';
import {
  createRequiredStringOfMaxLengthSchema,
  descriptionSchema,
} from './base.schema';

export const PermissionGroupSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  description: Schema.String,
  createdAt: Schema.String,
  updatedAt: Schema.String,
});

export type PermissionGroup = Schema.Schema.Type<typeof PermissionGroupSchema>;

export const PermissionGroupPermissionSchema = Schema.Struct({
  groupId: Schema.String,
  permissionId: Schema.String,
});

export type PermissionGroupPermission = Schema.Schema.Type<
  typeof PermissionGroupPermissionSchema
>;

const nameSchema = createRequiredStringOfMaxLengthSchema({
  fieldMaxLength: 50,
  errorMessages: {
    required: () => 'Name is required',
    maxLength: (issue) =>
      `Name must be ${issue._tag.length} characters or less`,
  },
});

const permissionIdsSchema = Schema.Array(Schema.String).pipe(
  Schema.minItems(1, {
    message: () => 'At least one permission is required',
  }),
);

/**
 * Schema for creating a new permission group
 */
export const PermissionGroupInputSchema = Schema.Struct({
  name: nameSchema,
  description: descriptionSchema,
  permissionIds: permissionIdsSchema,
});

export type CreatePermissionGroup = Schema.Schema.Type<
  typeof PermissionGroupInputSchema
>;

/**
 * Schema for permission group permissions
 */
export const PermissionGroupPermissionsSchema = Schema.Struct({
  permissionIds: permissionIdsSchema,
});

export type PermissionGroupPermissions = Schema.Schema.Type<
  typeof PermissionGroupPermissionsSchema
>;

/**
 * Schema for updating a permission group
 */
export const UpdatePermissionGroupSchema = Schema.Struct({
  name: nameSchema,
  description: descriptionSchema,
  ...PermissionGroupPermissionsSchema.fields,
});

export type UpdatePermissionGroup = Schema.Schema.Type<
  typeof UpdatePermissionGroupSchema
> & { id: string };
