import * as DbSchema from '@rie/db-schema/schemas';
import type { ExtractTablesWithRelations } from 'drizzle-orm';
import {
  type NodePgDatabase,
  type NodePgQueryResultHKT,
  drizzle,
} from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import * as Cause from 'effect/Cause';
import * as Context from 'effect/Context';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Layer from 'effect/Layer';
import * as Option from 'effect/Option';
import * as Redacted from 'effect/Redacted';
import * as Runtime from 'effect/Runtime';
import * as pg from 'pg';

type FullDbSchema = typeof DbSchema;

type TransactionClient = PgTransaction<
  NodePgQueryResultHKT,
  FullDbSchema,
  ExtractTablesWithRelations<FullDbSchema>
>;

type Client = NodePgDatabase<FullDbSchema> & {
  $client: pg.Pool;
};

type TransactionContextShape = {
  <U>(
    fn: (client: TransactionClient) => Promise<U>,
  ): Effect.Effect<U, DatabaseError>;
  rollback: () => Effect.Effect<void, never>;
};
export class TransactionContext extends Context.Tag('TransactionContext')<
  TransactionContext,
  TransactionContextShape
>() {
  public static readonly provide = (
    transaction: TransactionContextShape,
  ): (<A, E, R>(
    self: Effect.Effect<A, E, R>,
  ) => Effect.Effect<A, E, Exclude<R, TransactionContext>>) =>
    Effect.provideService(this, transaction);
}

export class DatabaseError extends Data.TaggedError('DatabaseError')<{
  readonly type:
    | 'unique_violation'
    | 'foreign_key_violation'
    | 'connection_error';
  readonly cause: pg.DatabaseError;
}> {
  public override toString() {
    return `DatabaseError: ${this.cause.message}`;
  }

  public get message() {
    return this.cause.message;
  }
}

const matchPgError = (error: unknown) => {
  if (error instanceof pg.DatabaseError) {
    switch (error.code) {
      case '23505':
        return new DatabaseError({ type: 'unique_violation', cause: error });
      case '23503':
        return new DatabaseError({
          type: 'foreign_key_violation',
          cause: error,
        });
      case '08000':
        return new DatabaseError({ type: 'connection_error', cause: error });
    }
  }
  return null;
};

export class DatabaseConnectionLostError extends Data.TaggedError(
  'DatabaseConnectionLostError',
)<{
  cause: unknown;
  message: string;
}> {}

export type Config = {
  url: Redacted.Redacted;
  ssl: boolean;
};

const makeService = (config: Config) =>
  Effect.gen(function* () {
    const pool = yield* Effect.acquireRelease(
      Effect.sync(
        () =>
          new pg.Pool({
            connectionString: Redacted.value(config.url),
            ssl: config.ssl,
          }),
      ),
      (pool) => Effect.promise(() => pool.end()),
    );

    yield* Effect.tryPromise(() => pool.query('SELECT 1')).pipe(
      Effect.timeoutFail({
        duration: '10 seconds',
        onTimeout: () =>
          new DatabaseConnectionLostError({
            cause: new Error('[Database] Failed to connect: timeout'),
            message: '[Database] Failed to connect: timeout',
          }),
      }),
      Effect.catchTag(
        'UnknownException',
        (error) =>
          new DatabaseConnectionLostError({
            cause: error.cause,
            message: '[Database] Failed to connect',
          }),
      ),
      Effect.tap(() =>
        Effect.logInfo(
          '[Database client]: Connection to the database established.',
        ),
      ),
    );

    const setupConnectionListeners = Effect.zipRight(
      Effect.async<void, DatabaseConnectionLostError>((resume) => {
        pool.on('error', (error) => {
          resume(
            Effect.fail(
              new DatabaseConnectionLostError({
                cause: error,
                message: error.message,
              }),
            ),
          );
        });

        return Effect.sync(() => {
          pool.removeAllListeners('error');
        });
      }),
      Effect.logInfo(
        '[Database client]: Connection error listeners initialized.',
      ),
      {
        concurrent: true,
      },
    );

    const db = drizzle(pool, {
      schema: DbSchema,
      casing: 'snake_case',
    });

    const execute = Effect.fn(<T>(fn: (client: Client) => Promise<T>) =>
      Effect.tryPromise({
        try: () => fn(db),
        catch: (cause) => {
          // console.log('error cause', JSON.stringify(cause, null, 2));
          const error = matchPgError(cause);
          console.log('error type', error instanceof DatabaseError);
          console.log('error', JSON.stringify(error, null, 2));
          if (error !== null) {
            return error;
          }
          throw cause;
        },
      }),
    );

    const transaction = Effect.fn('Database.transaction')(
      <T, E, R>(
        txExecute: (tx: TransactionContextShape) => Effect.Effect<T, E, R>,
      ) =>
        Effect.runtime<R>().pipe(
          Effect.map((runtime) => Runtime.runPromiseExit(runtime)),
          Effect.flatMap((runPromiseExit) =>
            Effect.async<T, DatabaseError | E, R>((resume) => {
              db.transaction(async (tx: TransactionClient) => {
                // Create a wrapper function with rollback capability
                const txWrapper = (
                  fn: <U>(client: TransactionClient) => Promise<U>,
                ) =>
                  Effect.tryPromise({
                    try: () => fn(tx),
                    catch: (cause) => {
                      const error = matchPgError(cause);
                      if (error !== null) {
                        return error;
                      }
                      throw cause;
                    },
                  });

                // Add rollback method
                const txWrapperWithRollback = Object.assign(txWrapper, {
                  rollback: () =>
                    Effect.sync(() => {
                      tx.rollback();
                    }),
                }) as TransactionContextShape;

                const result = await runPromiseExit(
                  txExecute(txWrapperWithRollback),
                );
                Exit.match(result, {
                  onSuccess: (value) => {
                    resume(Effect.succeed(value));
                  },
                  onFailure: (cause) => {
                    if (Cause.isFailure(cause)) {
                      resume(Effect.fail(Cause.originalError(cause) as E));
                    } else {
                      resume(Effect.die(cause));
                    }
                  },
                });
              }).catch((cause) => {
                const error = matchPgError(cause);
                resume(error !== null ? Effect.fail(error) : Effect.die(cause));
              });
            }),
          ),
        ),
    );

    type ExecuteFn = <T>(
      fn: (client: Client | TransactionClient) => Promise<T>,
    ) => Effect.Effect<T, DatabaseError>;
    const makeQuery =
      <A, E, R, Input = never>(
        queryFn: (executeFn: ExecuteFn, input: Input) => Effect.Effect<A, E, R>,
      ) =>
      (
        ...args: [Input] extends [never] ? [] : [input: Input]
      ): Effect.Effect<A, E, R> => {
        const input = args[0] as Input;
        return Effect.serviceOption(TransactionContext).pipe(
          Effect.map(Option.getOrNull),
          Effect.flatMap((txOrNull) => queryFn(txOrNull ?? execute, input)),
        );
      };

    return {
      execute,
      transaction,
      setupConnectionListeners,
      makeQuery,
    } as const;
  });

type Shape = Effect.Effect.Success<ReturnType<typeof makeService>>;

export class PgDatabase extends Effect.Tag('PgDatabase')<PgDatabase, Shape>() {}

export const pgLayer = (config: Config) =>
  Layer.scoped(PgDatabase, makeService(config));
