import type { PermissionFormSchema } from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { DbPermission } from '@rie/db-schema/entity-types';

export const getAllPermissions = async () => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.get<DbPermission[]>('v2/permissions').json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to get permissions');
  }
};

export const createPermission = async (payload: PermissionFormSchema) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.post<DbPermission>('v2/permissions', {
      json: payload,
    });
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to create permission');
  }
};

export const updatePermission = async ({
  id,
  payload,
}: { id: string; payload: PermissionFormSchema }) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.put<DbPermission>(`v2/permissions/${id}`, {
      json: payload,
    });
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to update permission');
  }
};

export const deletePermission = async (id: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(`v2/permissions/${id}`);
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to delete permission');
  }
};
