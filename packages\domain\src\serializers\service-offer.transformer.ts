import {
  type ServiceOfferRawDTO,
  ServiceOfferRawSchema,
  type ServiceOfferResponseDTO,
  ServiceOfferResponseSchema,
} from '@/schemas';
import { getTranslatedFieldFromTranslations } from '@/utils/database.utils';
import * as Either from 'effect/Either';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

// Create the transformation schema
export const serviceOfferRawToResponse = (
  requestedLocale: string,
  fallbackLocale: string,
  rawServiceOffer: ServiceOfferRawDTO,
): ServiceOfferResponseDTO => {
  const ServiceOfferFromRaw = Schema.transformOrFail(
    ServiceOfferRawSchema,
    ServiceOfferResponseSchema,
    {
      strict: false,
      decode: (raw, _options, ast) => {
        return ParseResult.try({
          try: () => {
            return {
              ...raw,
              name: getTranslatedFieldFromTranslations(
                raw.translations,
                'name',
                requestedLocale,
                fallbackLocale,
              ),
              description: getTranslatedFieldFromTranslations(
                raw.translations,
                'description',
                requestedLocale,
                fallbackLocale,
              ),
              serviceConditions: getTranslatedFieldFromTranslations(
                raw.translations,
                'serviceConditions',
                requestedLocale,
                fallbackLocale,
              ),
            };
          },
          catch: (error) =>
            new ParseResult.Type(
              ast,
              raw,
              error instanceof Error ? error.message : 'Failed to parse object',
            ),
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  const result = Schema.decodeEither(ServiceOfferFromRaw)(rawServiceOffer);

  if (Either.isLeft(result)) {
    throw new Error(
      `Failed to transform service offer: ${JSON.stringify(result.left)}`,
    );
  }

  return result.right;
};
