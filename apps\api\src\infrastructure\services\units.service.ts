import { UnitsRepositoryLive } from '@/infrastructure/repositories/units.repository';
import {
    CreateUnitPayload,
    UpdateUnitPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class UnitsServiceLive extends Effect.Service<UnitsServiceLive>()(
    'UnitsServiceLive',
    {
        dependencies: [UnitsRepositoryLive],
        effect: Effect.gen(function* () {
            const unitsRepository = yield* UnitsRepositoryLive;

            const getAllUnits = () => {
                return Effect.gen(function* () {
                    return yield* unitsRepository.findAll();
                });
            };

            const getUnitById = (id: string) => {
                return Effect.gen(function* () {
                    const unit = yield* unitsRepository.findById(id);
                    if (!unit) {
                        return yield* Effect.fail(new Error(`Unit with id ${id} not found`));
                    }
                    return unit;
                });
            };

            const createUnit = (data: CreateUnitPayload) => {
                return Effect.gen(function* () {
                    return yield* unitsRepository.createWithTranslations({
                        unit: {
                            institutionId: data.institutionId,
                            modifiedBy: data.modifiedBy,
                        },
                        translations: data.translations ? [...data.translations] : [],
                    });
                });
            };

            const updateUnit = (params: UpdateUnitPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingUnit = yield* unitsRepository.findById(id);
                    if (!existingUnit) {
                        return yield* Effect.fail(new Error(`Unit with id ${id} not found`));
                    }

                    return yield* unitsRepository.updateOne({
                        id,
                        institutionId: updateData.institutionId,
                        modifiedBy: updateData.modifiedBy,
                    });
                });
            };

            const deleteUnit = (id: string) => {
                return Effect.gen(function* () {
                    const existingUnit = yield* unitsRepository.findById(id);
                    if (!existingUnit) {
                        return yield* Effect.fail(new Error(`Unit with id ${id} not found`));
                    }
                    return yield* unitsRepository.deleteOne(id);
                });
            };

            return {
                getAllUnits,
                getUnitById,
                createUnit,
                updateUnit,
                deleteUnit,
            } as const;
        }),
    },
) { }