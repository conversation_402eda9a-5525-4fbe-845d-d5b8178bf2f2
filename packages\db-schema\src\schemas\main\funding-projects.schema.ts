import {
  equipmentAssociatedFundingProjects,
  infrastructures,
  locales,
  people,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  date,
  integer,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const fundingProjects = pgTable(
  'funding_projects',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    holderId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    typeId: text()
      .notNull()
      .references(() => fundingProjectTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    fciId: text(),
    synchroId: text().notNull(),
    obtainingYear: integer().notNull(),
    endDate: date(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      synchroIdUnique: unique().on(table.synchroId),
    },
  ],
);

export const fundingProjectsRelations = relations(
  fundingProjects,
  ({ one, many }) => ({
    holder: one(people, {
      fields: [fundingProjects.holderId],
      references: [people.id],
    }),
    type: one(fundingProjectTypes, {
      fields: [fundingProjects.typeId],
      references: [fundingProjectTypes.id],
    }),
    translations: many(fundingProjectsI18N),
    identifiers: many(fundingProjectAssociatedIdentifiers),
    associatedPeople: many(fundingProjectAssociatedPeople),
    associatedInfrastructures: many(fundingProjectInfrastructures),
    associatedEquipment: many(equipmentAssociatedFundingProjects),
  }),
);

export const fundingProjectsI18N = pgTable(
  'funding_projects_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => fundingProjects.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text().notNull(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const fundingProjectsI18NRelations = relations(
  fundingProjectsI18N,
  ({ one }) => ({
    fundingProject: one(fundingProjects, {
      fields: [fundingProjectsI18N.dataId],
      references: [fundingProjects.id],
    }),
    locale: one(locales, {
      fields: [fundingProjectsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const fundingProjectTypes = pgTable('funding_project_types', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const fundingProjectTypeRelations = relations(
  fundingProjectTypes,
  ({ many }) => ({
    fundingProjects: many(fundingProjects),
    translations: many(fundingProjectTypesI18N),
  }),
);

export const fundingProjectTypesI18N = pgTable(
  'funding_project_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => fundingProjectTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text().notNull(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const fundingProjectTypesI18NRelations = relations(
  fundingProjectTypesI18N,
  ({ one }) => ({
    fundingProjectType: one(fundingProjectTypes, {
      fields: [fundingProjectTypesI18N.dataId],
      references: [fundingProjectTypes.id],
    }),
    locale: one(locales, {
      fields: [fundingProjectTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const fundingProjectAssociatedPeople = pgTable(
  'funding_project_associated_people',
  {
    fundingProjectId: text()
      .notNull()
      .references(() => fundingProjects.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      fundingProjectPersonPk: primaryKey({
        columns: [table.fundingProjectId, table.personId],
      }),
    },
  ],
);

export const fundingProjectPeopleRelations = relations(
  fundingProjectAssociatedPeople,
  ({ one }) => ({
    fundingProject: one(fundingProjects, {
      fields: [fundingProjectAssociatedPeople.fundingProjectId],
      references: [fundingProjects.id],
    }),
    person: one(people, {
      fields: [fundingProjectAssociatedPeople.personId],
      references: [people.id],
    }),
  }),
);

export const fundingProjectInfrastructures = pgTable(
  'funding_project_infrastructures',
  {
    fundingProjectId: text()
      .notNull()
      .references(() => fundingProjects.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uniqueProjectInfrastructure: primaryKey({
        columns: [table.fundingProjectId, table.infrastructureId],
      }),
    },
  ],
);

export const fundingProjectInfrastructuresRelations = relations(
  fundingProjectInfrastructures,
  ({ one }) => ({
    fundingProject: one(fundingProjects, {
      fields: [fundingProjectInfrastructures.fundingProjectId],
      references: [fundingProjects.id],
    }),
    infrastructure: one(infrastructures, {
      fields: [fundingProjectInfrastructures.infrastructureId],
      references: [infrastructures.id],
    }),
  }),
);

export const fundingProjectIdentifierTypes = pgTable(
  'funding_project_identifier_types',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
);

export const fundingProjectIdentifierTypesRelations = relations(
  fundingProjectIdentifierTypes,
  ({ many }) => ({
    fundingProjectIdentifiers: many(fundingProjectAssociatedIdentifiers),
    translations: many(fundingProjectIdentifierTypesI18N),
  }),
);

export const fundingProjectIdentifierTypesI18N = pgTable(
  'funding_project_identifier_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => fundingProjectIdentifierTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const fundingProjectIdentifierTypeI18NRelations = relations(
  fundingProjectIdentifierTypesI18N,
  ({ one }) => ({
    identifierType: one(fundingProjectIdentifierTypes, {
      fields: [fundingProjectIdentifierTypesI18N.dataId],
      references: [fundingProjectIdentifierTypes.id],
    }),
    locale: one(locales, {
      fields: [fundingProjectIdentifierTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const fundingProjectAssociatedIdentifiers = pgTable(
  'funding_project_associated_identifiers',
  {
    fundingProjectId: text()
      .notNull()
      .references(() => fundingProjects.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    identifierTypeId: text()
      .notNull()
      .references(() => fundingProjectIdentifierTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    identifier: text().notNull(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uniqueProjectIdentifier: primaryKey({
        columns: [table.fundingProjectId, table.identifierTypeId],
      }),
    },
  ],
);

export const fundingProjectAssociatedIdentifiersRelations = relations(
  fundingProjectAssociatedIdentifiers,
  ({ one }) => ({
    fundingProject: one(fundingProjects, {
      fields: [fundingProjectAssociatedIdentifiers.fundingProjectId],
      references: [fundingProjects.id],
    }),
    identifierType: one(fundingProjectIdentifierTypes, {
      fields: [fundingProjectAssociatedIdentifiers.identifierTypeId],
      references: [fundingProjectIdentifierTypes.id],
    }),
  }),
);
