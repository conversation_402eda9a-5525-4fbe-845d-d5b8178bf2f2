import * as Schema from 'effect/Schema';

// — Translation shape
export const CampusTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.optional(Schema.String),
});

export type CampusTranslation = Schema.Schema.Type<typeof CampusTranslationSchema>;

// — Full Campus shape
export const CampusSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  sadId: Schema.optional(Schema.String),         // nullable
  institutionId: Schema.String,                  // required
  translations: Schema.Array(CampusTranslationSchema), // at least []
  createdAt: Schema.String,                      // ISO timestamp
  updatedAt: Schema.String,                      // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),   // nullable
});

export type Campus = Schema.Schema.Type<typeof CampusSchema>;

// — Input schemas for API
export const CreateCampusSchema = Schema.Struct({
  sadId: Schema.optional(Schema.String),
  institutionId: Schema.String,
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
    })
  ),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateCampusPayload = Schema.Schema.Type<typeof CreateCampusSchema>;

export const UpdateCampusSchema = Schema.Struct({
  sadId: Schema.optional(Schema.String),
  institutionId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
    })
  )),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateCampusPayload = Schema.Schema.Type<typeof UpdateCampusSchema> & { id: string };

// — Response schemas
export const CampusResponseSchema = CampusSchema;
export const CampusListResponseSchema = Schema.Array(CampusResponseSchema);

export type CampusResponse = Schema.Schema.Type<typeof CampusResponseSchema>;
export type CampusListResponse = Schema.Schema.Type<typeof CampusListResponseSchema>;
