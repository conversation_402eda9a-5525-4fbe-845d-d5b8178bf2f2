// import { ConfigLive } from '@/infrastructure/config/config.live';
// import { addresses } from '@rie/db-schema/schemas';
// import { Database } from '@rie/postgres-db';
// import { eq } from 'drizzle-orm';
// import * as Effect from 'effect/Effect';
// import * as Layer from 'effect/Layer';

// const PgDatabaseLive = Layer.unwrapEffect(
//   ConfigLive.pipe(
//     Effect.map((env) =>
//       Database.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
//     ),
//   ),
// ).pipe(Layer.provide(ConfigLive.Default));

// export class AddressesRepositoryLive extends Effect.Service<AddressesRepositoryLive>()(
//   'AddressesRepositoryLive',
//   {
//     dependencies: [PgDatabaseLive],
//     effect: Effect.gen(function* ($) {
//       const db = yield* $(Database.PgDatabase);

//       const findAllAddresses = db.makeQuery((exec) =>
//         exec((client) =>
//           client.query.addresses.findMany({
//             columns: {
//               id: true,
//               addressType: true,
//               campusAddressId: true,
//               civicAddressId: true,
//             },
//             with: {
//               campusAddress: {
//                 columns: {
//                   id: true,
//                   room_id: true,
//                   createdAt: true,
//                   updatedAt: true,
//                 },
//               },
//               civicAddress: {
//                 columns: {
//                   id: true,
//                   street1: true,
//                   street2: true,
//                   city: true,
//                   state: true,
//                   postalCode: true,
//                   countryCode: true,
//                   placeId: true,
//                   lat: true,
//                   lon: true,
//                   createdAt: true,
//                   updatedAt: true,
//                 },
//               },
//             },
//           }),
//         ),
//       );

//       const findAddressById = db.makeQuery((exec, id: string) =>
//         exec((client) =>
//           client.query.addresses.findFirst({
//             where: eq(addresses.id, id),
//             columns: {
//               id: true,
//               addressType: true,
//               campusAddressId: true,
//               civicAddressId: true,
//             },
//             with: {
//               campusAddress: {
//                 columns: {
//                   id: true,
//                   room_id: true,
//                   createdAt: true,
//                   updatedAt: true,
//                 },
//               },
//               civicAddress: {
//                 columns: {
//                   id: true,
//                   street1: true,
//                   street2: true,
//                   city: true,
//                   state: true,
//                   postalCode: true,
//                   countryCode: true,
//                   placeId: true,
//                   lat: true,
//                   lon: true,
//                   createdAt: true,
//                   updatedAt: true,
//                 },
//               },
//             },
//           }),
//         ),
//       );

//       const createAddress = db.makeQuery(
//         (
//           exec,
//           params: {
//             addressType: 'campus' | 'civic';
//             campusAddressId?: string | null;
//             civicAddressId?: string | null;
//           },
//         ) =>
//           exec((client) =>
//             client
//               .insert(addresses)
//               .values({
//                 addressType: params.addressType,
//                 campusAddressId: params.campusAddressId,
//                 civicAddressId: params.civicAddressId,
//               })
//               .returning({
//                 id: addresses.id,
//                 addressType: addresses.addressType,
//                 campusAddressId: addresses.campusAddressId,
//                 civicAddressId: addresses.civicAddressId,
//               }),
//           ),
//       );

//       const updateAddress = db.makeQuery(
//         (
//           exec,
//           params: {
//             id: string;
//             addressType?: 'campus' | 'civic';
//             campusAddressId?: string | null;
//             civicAddressId?: string | null;
//           },
//         ) =>
//           exec((client) =>
//             client
//               .update(addresses)
//               .set({
//                 addressType: params.addressType,
//                 campusAddressId: params.campusAddressId,
//                 civicAddressId: params.civicAddressId,
//               })
//               .where(eq(addresses.id, params.id))
//               .returning({
//                 id: addresses.id,
//                 addressType: addresses.addressType,
//                 campusAddressId: addresses.campusAddressId,
//                 civicAddressId: addresses.civicAddressId,
//               }),
//           ),
//       );

//       const deleteAddress = db.makeQuery((exec, id: string) =>
//         exec((client) =>
//           client
//             .delete(addresses)
//             .where(eq(addresses.id, id))
//             .returning({ id: addresses.id }),
//         ),
//       );

//       return {
//         findAllAddresses,
//         findAddressById,
//         createAddress,
//         updateAddress,
//         deleteAddress,
//       } as const;
//     }),
//   },
// ) { }
