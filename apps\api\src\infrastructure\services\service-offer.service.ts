import { LocaleRepositoryLive } from '@/infrastructure/repositories/locale.repository';
import { ServiceOfferRepositoryLive } from '@/infrastructure/repositories/service-offer.repository';
import { Transformers } from '@rie/domain';
import { Effect } from 'effect';

export class ServiceOfferServiceLive extends Effect.Service<ServiceOfferServiceLive>()(
  'ServiceOfferServiceLive',
  {
    dependencies: [
      ServiceOfferRepositoryLive.Default,
      LocaleRepositoryLive.Default,
    ],
    effect: Effect.gen(function* () {
      const findById = (id: string, locale: string) => {
        return Effect.gen(function* () {
          const serviceOfferRepository = yield* ServiceOfferRepositoryLive;
          const localeRepository = yield* LocaleRepositoryLive;
          const fallbackLocale =
            yield* localeRepository.getFallbackLocale(locale);

          const maybeRawServiceOffer =
            yield* serviceOfferRepository.findById(id);

          if (!maybeRawServiceOffer) {
            return yield* Effect.fail(new Error('Service Offer not found'));
          }

          return Transformers.serviceOfferRawToResponse(
            locale,
            fallbackLocale,
            maybeRawServiceOffer,
          );
        });
      };

      return { findById } as const;
    }),
  },
) {}
