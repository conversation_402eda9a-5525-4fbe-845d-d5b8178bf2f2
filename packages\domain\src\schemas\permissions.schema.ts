import type {
  DbPermissionAction,
  DbPermissionDomain,
} from '@rie/db-schema/entity-types';
import { domainEnum, permissionActionEnum } from '@rie/db-schema/schemas';
import * as Schema from 'effect/Schema';

export const PermissionDomainSchema = Schema.Literal(...domainEnum.enumValues);
export const PermissionActionSchema = Schema.Literal(
  ...permissionActionEnum.enumValues,
);

export const PermissionSchema = Schema.Struct({
  id: Schema.String,
  domain: PermissionDomainSchema,
  action: PermissionActionSchema,
  createdAt: Schema.String,
  updatedAt: Schema.String,
});

export type Permission = `${DbPermissionDomain}:${DbPermissionAction}`;

export const PermissionInputSchema = Schema.Struct({
  action: PermissionActionSchema,
  domain: PermissionDomainSchema,
});
