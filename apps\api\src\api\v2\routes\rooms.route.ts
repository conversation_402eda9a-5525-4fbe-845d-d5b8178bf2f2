import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RoomsRuntime } from '@/infrastructure/runtimes/rooms.runtime';
import { RoomsServiceLive } from '@/infrastructure/services/rooms.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateRoomSchema,
    UpdateRoomSchema
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Import additional schemas
import { ResourceIdSchema, RoomSchema } from '@rie/domain/schemas';

// OpenAPI route descriptions
export const getAllRoomsRoute = describeRoute({
    description: 'Get all Rooms',
    operationId: 'getAllRooms',
    responses: {
        200: {
            description: 'List of rooms',
            content: { 'application/json': { schema: resolver(Schema.Array(RoomSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Rooms'],
});

export const getRoomByIdRoute = describeRoute({
    description: 'Get a Room by ID',
    operationId: 'getRoomById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Room found',
            content: { 'application/json': { schema: resolver(RoomSchema) } },
        },
        404: {
            description: 'Room not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Rooms'],
});

export const createRoomRoute = describeRoute({
    description: 'Create a Room',
    operationId: 'createRoom',
    requestBody: {
        required: true,
        content: {
            'application/json': {
                schema: resolver(CreateRoomSchema),
                example: {
                    number: "NEW-ROOM-001",
                    area: 25.5,
                    floorLoad: null,
                    buildingId: "bw6c4rifj8239sc6gheruphi"
                }
            },
        },
    },
    responses: {
        201: {
            description: 'Room created',
            content: { 'application/json': { schema: resolver(RoomSchema) } },
        },
        400: { description: 'Validation error - Invalid input data or missing required fields' },
        404: { description: 'Foreign key not found - Building does not exist' },
        500: { description: 'Internal server error' },
    },
    tags: ['Rooms'],
});

export const updateRoomRoute = describeRoute({
    description: 'Update a Room',
    operationId: 'updateRoom',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Room ID (CUID format)',
            example: 'vot1nira0alg10houmycxefw'
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': {
                schema: resolver(UpdateRoomSchema),
                example: {
                    number: "UPDATED-ROOM-001",
                    area: 30.0,
                    floorLoad: null,
                    buildingId: "bw6c4rifj8239sc6gheruphi"
                }
            },
        },
    },
    responses: {
        200: {
            description: 'Room updated',
            content: { 'application/json': { schema: resolver(RoomSchema) } },
        },
        400: { description: 'Validation error - Invalid input data' },
        404: { description: 'Room not found or Building does not exist' },
        500: { description: 'Internal server error' },
    },
    tags: ['Rooms'],
});

export const deleteRoomRoute = describeRoute({
    description: 'Delete a Room',
    operationId: 'deleteRoom',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Room deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Room not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Rooms'],
});

const roomsRoute = new Hono();

roomsRoute.get('/', getAllRoomsRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.getAllRooms();
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.get('/:id', getRoomByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.getRoomById(id);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.post('/', createRoomRoute, effectValidator('json', CreateRoomSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.createRoom(body);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.put('/:id', updateRoomRoute, effectValidator('json', UpdateRoomSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.updateRoom({ id, ...body });
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.delete('/:id', deleteRoomRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.deleteRoom(id);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Room deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { roomsRoute };
