import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RoomsRuntime } from '@/infrastructure/runtimes/rooms.runtime';
import { RoomsServiceLive } from '@/infrastructure/services/rooms.service';
import { effectValidator } from '@hono/effect-validator';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateRoomSchema,
    UpdateRoomSchema
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

const roomsRoute = new Hono();

roomsRoute.get('/', async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.getAllRooms();
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.get('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.getRoomById(id);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.post('/', effectValidator('json', CreateRoomSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.createRoom(body);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.put('/:id', effectValidator('json', UpdateRoomSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.updateRoom({ id, ...body });
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

roomsRoute.delete('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* RoomsServiceLive;
        return yield* svc.deleteRoom(id);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Room deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { roomsRoute };
