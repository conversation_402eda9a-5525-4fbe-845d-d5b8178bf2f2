import {
  addresses,
  equipments,
  fundingProjectInfrastructures,
  guids,
  innovationLabs,
  locales,
  people,
  users,
  visibilities,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const infrastructures = pgTable('infrastructures', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  guidId: text()
    .notNull()
    .references(() => guids.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  typeId: text()
    .notNull()
    .references(() => infrastructureTypes.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  addressId: text().references(() => addresses.id, {
    onDelete: 'cascade',
    onUpdate: 'cascade',
  }),
  statusId: text()
    .notNull()
    .references(() => infrastructureStatuses.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  website: text(),
  is_featured: boolean(),
  visibilityId: text()
    .notNull()
    .references(() => visibilities.id),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const infrastructuresRelations = relations(
  infrastructures,
  ({ one, many }) => ({
    guid: one(guids, {
      fields: [infrastructures.guidId],
      references: [guids.id],
    }),
    address: one(addresses, {
      fields: [infrastructures.addressId],
      references: [addresses.id],
    }),
    type: one(infrastructureTypes, {
      fields: [infrastructures.typeId],
      references: [infrastructureTypes.id],
    }),
    status: one(infrastructureStatuses, {
      fields: [infrastructures.statusId],
      references: [infrastructureStatuses.id],
    }),
    visibility: one(visibilities, {
      fields: [infrastructures.visibilityId],
      references: [visibilities.id],
    }),
    translations: many(infrastructuresI18N),
    associatedInnovationLabs: many(infrastructureAssociatedInnovationLabs),
    associatedPersons: many(infrastructureAssociatedPeople),
    associatedScientificManagers: many(
      infrastructureAssociatedScientificManagers,
    ),
    associatedOperationalManagers: many(
      infrastructureAssociatedOperationalManagers,
    ),
    associatedSstManagers: many(infrastructureAssociatedSstManagers),
    equipment: many(equipments),
    fundingProjects: many(fundingProjectInfrastructures),
  }),
);

export const infrastructuresI18N = pgTable(
  'infrastructures_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    otherNames: text(),
    acronyms: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const infrastructuresI18NRelations = relations(
  infrastructuresI18N,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructuresI18N.dataId],
      references: [infrastructures.id],
    }),
    locale: one(locales, {
      fields: [infrastructuresI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const infrastructureAssociatedInnovationLabs = pgTable(
  'infrastructure_associated_innovation_labs',
  {
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    innovationLabId: text()
      .notNull()
      .references(() => innovationLabs.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      infrastructureInnovationLabPk: primaryKey({
        columns: [table.infrastructureId, table.innovationLabId],
      }),
    },
  ],
);

export const infrastructureAssociatedPeople = pgTable(
  'infrastructure_associated_people',
  {
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    roleTypeId: text()
      .notNull()
      .references(() => infrastructureRoleTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      infrastructurePersonPk: primaryKey({
        columns: [table.infrastructureId, table.personId, table.roleTypeId],
      }),
    },
  ],
);

export const infrastructureAssociatedScientificManagers = pgTable(
  'infrastructure_associated_scientific_managers',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      infrastructureSuperVisorPk: primaryKey({
        columns: [table.infrastructureId, table.personId],
      }),
    },
  ],
);

export const infrastructureAssociatedScientificManagersRelations = relations(
  infrastructureAssociatedScientificManagers,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedScientificManagers.infrastructureId],
      references: [infrastructures.id],
    }),
    person: one(people, {
      fields: [infrastructureAssociatedScientificManagers.personId],
      references: [people.id],
    }),
  }),
);

export const infrastructureAssociatedOperationalManagers = pgTable(
  'infrastructure_associated_operational_managers',
  {
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      infrastructureOperationalManagerPk: primaryKey({
        columns: [table.infrastructureId, table.personId],
      }),
    },
  ],
);

export const infrastructureAssociatedOperationalManagersRelations = relations(
  infrastructureAssociatedOperationalManagers,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedOperationalManagers.infrastructureId],
      references: [infrastructures.id],
    }),
    person: one(people, {
      fields: [infrastructureAssociatedOperationalManagers.personId],
      references: [people.id],
    }),
  }),
);

export const infrastructureAssociatedSstManagers = pgTable(
  'infrastructure_associated_sst_managers',
  {
    infrastructureId: text()
      .notNull()
      .references(() => infrastructures.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      infrastructureSstManagerPk: primaryKey({
        columns: [table.infrastructureId, table.personId],
      }),
    },
  ],
);

export const infrastructureAssociatedSstManagersRelations = relations(
  infrastructureAssociatedSstManagers,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedSstManagers.infrastructureId],
      references: [infrastructures.id],
    }),
    person: one(people, {
      fields: [infrastructureAssociatedSstManagers.personId],
      references: [people.id],
    }),
  }),
);

export const infrastructureRoleTypes = pgTable('infrastructure_role_types', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const infrastructureStatuses = pgTable('infrastructure_statuses', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const infrastructureStatusesI18N = pgTable(
  'infrastructure_statuses_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => infrastructureStatuses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      dataIdUnique: unique().on(table.dataId, table.locale),
      localeIdUnique: unique().on(table.locale, table.name),
    },
  ],
);

export const infrastructureTypes = pgTable(
  'infrastructure_types',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    uid: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const infrastructureTypesI18N = pgTable(
  'infrastructure_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => infrastructureTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const infrastructureAssociatedInnovationLabsRelations = relations(
  infrastructureAssociatedInnovationLabs,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedInnovationLabs.infrastructureId],
      references: [infrastructures.id],
    }),
    innovationLab: one(innovationLabs, {
      fields: [infrastructureAssociatedInnovationLabs.innovationLabId],
      references: [innovationLabs.id],
    }),
  }),
);

export const infrastructuresAssociatedPersonRelations = relations(
  infrastructureAssociatedPeople,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedPeople.infrastructureId],
      references: [infrastructures.id],
    }),
    person: one(people, {
      fields: [infrastructureAssociatedPeople.personId],
      references: [people.id],
    }),
    roleType: one(infrastructureRoleTypes, {
      fields: [infrastructureAssociatedPeople.roleTypeId],
      references: [infrastructureRoleTypes.id],
    }),
  }),
);

export const infrastructureAssociatedSupervisorsRelations = relations(
  infrastructureAssociatedScientificManagers,
  ({ one }) => ({
    infrastructures: one(infrastructures, {
      fields: [infrastructureAssociatedScientificManagers.infrastructureId],
      references: [infrastructures.id],
    }),
    person: one(people, {
      fields: [infrastructureAssociatedScientificManagers.personId],
      references: [people.id],
    }),
  }),
);
