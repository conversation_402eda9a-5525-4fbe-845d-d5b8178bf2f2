{"name": "umontreal-rie", "version": "1.0.0", "private": true, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "format": "turbo run format", "lint": "turbo run lint", "test": "turbo run test", "e2e:dev": "turbo run dev:test", "e2e:ci": "turbo run e2e:test", "prepare": "husky", "syncpack:list": "syncpack list-mismatches", "syncpack:fix": "syncpack fix-mismatches", "build:packages": "turbo run build --filter=\"@rie/auth\" --filter=\"@rie/domain\" --filter=\"@rie/postgres-db\" --filter=\"@rie/utils\""}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rie/biome-config": "workspace:*", "husky": "^9.1.7", "syncpack": "^13.0.4", "tsc-alias": "^1.8.16", "turbo": "^2.5.3", "typescript": "^5.8.3"}, "engines": {"node": "^20.0.0", "pnpm": "^10.10.0"}, "packageManager": "pnpm@10.10.0"}