import { handleEffectError } from '@/api/v2/utils/error-handler';
import { BuildingsRuntime } from '@/infrastructure/runtimes/buildings.runtime';
import { BuildingsServiceLive } from '@/infrastructure/services/buildings.service';
import { effectValidator } from '@hono/effect-validator';
import {
  BuildingInputSchema,
  BuildingSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

export const createBuildingRoute = describeRoute({
  description: 'Create a Building',
  operationId: 'createBuilding',
  requestBody: {
    required: true,
    content: {
      'application/json': { schema: resolver(BuildingInputSchema) },
    },
  },
  responses: {
    201: {
      description: 'Building created',
      content: { 'application/json': { schema: resolver(BuildingSchema) } },
    },
    400: { /* validation error */ },
    404: { /* FK not found */ },
    500: { /* generic */ },
  },
  tags: ['Buildings'],
});

export const updateBuildingRoute = describeRoute({
  description: 'Update a Building',
  operationId: 'updateBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': { schema: resolver(BuildingInputSchema) },
    },
  },
  responses: {
    200: {
      description: 'Building updated',
      content: { 'application/json': { schema: resolver(BuildingSchema) } },
    },
    400: {},
    404: {},
    500: {},
  },
  tags: ['Buildings'],
});

export const deleteBuildingRoute = describeRoute({
  description: 'Delete a Building',
  operationId: 'deleteBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  responses: {
    200: {
      description: 'Building deleted',
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
    },
    404: {},
    500: {},
  },
  tags: ['Buildings'],
});

export const getBuildingByIdRoute = describeRoute({
  description: 'Get a Building by ID',
  operationId: 'getBuildingById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  responses: {
    200: {
      description: 'Building found',
      content: { 'application/json': { schema: resolver(BuildingSchema) } },
    },
    404: {
      description: 'Building not found',
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ error: Schema.String }),
          ),
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ error: Schema.String }),
          ),
        },
      },
    },
  },
  tags: ['Buildings'],
});

export const getAllBuildingsRoute = describeRoute({
  description: 'List all Buildings',
  operationId: 'getAllBuildings',
  responses: {
    200: {
      description: 'Buildings returned',
      content: {
        'application/json': { schema: resolver(Schema.Array(BuildingSchema)) },
      },
    },
    500: {},
  },
  tags: ['Buildings'],
});

const buildingsRoute = new Hono();

buildingsRoute.get(
  '/',
  getAllBuildingsRoute,
  async (ctx) => {
    const program = Effect.gen(function* () {
      const svc = yield* BuildingsServiceLive;
      return yield* svc.getAllBuildings();
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.get(
  '/:id',
  getBuildingByIdRoute,
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const svc = yield* BuildingsServiceLive;
      return yield* svc.getBuildingById(id);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.post(
  '/',
  createBuildingRoute,
  effectValidator('json', BuildingInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const svc = yield* BuildingsServiceLive;
      return yield* svc.createBuilding(body);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.put(
  '/:id',
  updateBuildingRoute,
  effectValidator('json', BuildingInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const svc = yield* BuildingsServiceLive;
      return yield* svc.updateBuilding({ id, ...body });
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.delete(
  '/:id',
  deleteBuildingRoute,
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const svc = yield* BuildingsServiceLive;
      return yield* svc.deleteBuilding(id);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Building deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { buildingsRoute };
