import {
  equipmentAssociatedOperationalManagers,
  equipmentAssociatedSSTManagers,
  equipments,
  fundingProjectAssociatedPeople,
  guids,
  infrastructureAssociatedPeople,
  infrastructureAssociatedScientificManagers,
  institutionAssociatedPeople,
  locales,
  media,
  unitAssociatedPeople,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  index,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const people = pgTable(
  'peoples',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    guidId: text().references(() => guids.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    uid: text(),
    firstName: text().notNull(),
    lastName: text().notNull(),
    userId: text().references(() => users.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      firstNameIdx: index().on(table.firstName),
      lastNameIdx: index().on(table.lastName),
      guidIdUnique: unique().on(table.guidId),
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const peopleRelations = relations(people, ({ one, many }) => ({
  guid: one(guids, {
    fields: [people.guidId],
    references: [guids.id],
  }),
  media: many(peopleAssociatedMedia),
  emails: many(peopleAssociatedEmails),
  phones: many(peopleAssociatedPhones),
  associatedUnits: many(unitAssociatedPeople),
  associatedInstitutions: many(institutionAssociatedPeople),
  managedEquipments: many(equipments),
  operatedEquipments: many(equipmentAssociatedOperationalManagers),
  sstEquipments: many(equipmentAssociatedSSTManagers),
  associatedInfrastructures: many(infrastructureAssociatedPeople),
  infrastructureResponsibilities: many(
    infrastructureAssociatedScientificManagers,
  ),
  supervisedInfrastructures: many(infrastructureAssociatedScientificManagers),
  associatedFundingProjects: many(fundingProjectAssociatedPeople),
  user: one(users, {
    fields: [people.userId],
    references: [users.id],
  }),
  // Location relations
  // emplacements: many(peoplesEmplacement),
}));

export const peopleAssociatedMedia = pgTable(
  'people_associated_media',
  {
    mediaId: text()
      .notNull()
      .references(() => media.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
  },
  (table) => [
    {
      personMediaPk: primaryKey({ columns: [table.mediaId, table.personId] }),
    },
  ],
);

export const peopleMediaRelations = relations(
  peopleAssociatedMedia,
  ({ one }) => ({
    media: one(media, {
      fields: [peopleAssociatedMedia.mediaId],
      references: [media.id],
    }),
    person: one(people, {
      fields: [peopleAssociatedMedia.personId],
      references: [people.id],
    }),
  }),
);

export const peopleAssociatedEmails = pgTable('people_associated_emails', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  personId: text()
    .notNull()
    .references(() => people.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
  address: text().notNull().unique(),
});

export const peopleAssociatedEmailsRelations = relations(
  peopleAssociatedEmails,
  ({ one }) => ({
    person: one(people, {
      fields: [peopleAssociatedEmails.personId],
      references: [people.id],
    }),
  }),
);

export const peopleAssociatedPhones = pgTable(
  'people_associated_phones',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    personId: text()
      .notNull()
      .references(() => people.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),

    phoneNumber: text(),
  },
  (table) => [
    {
      uniqueFullNumber: unique().on(table.personId, table.phoneNumber),
    },
  ],
);

export const peopleAssociatedPhonesRelations = relations(
  peopleAssociatedPhones,
  ({ one }) => ({
    person: one(people, {
      fields: [peopleAssociatedPhones.personId],
      references: [people.id],
    }),
  }),
);

export const peopleRoleTypes = pgTable('people_role_types', {
  id: text()
    .$defaultFn(() => DbUtils.cuid2())
    .primaryKey(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const peopleRoleTypesRelations = relations(
  peopleRoleTypes,
  ({ many }) => ({
    translations: many(peopleRoleTypesI18N),
  }),
);

export const peopleRoleTypesI18N = pgTable(
  'people_role_types_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => peopleRoleTypes.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const peopleRoleTypesI18NRelations = relations(
  peopleRoleTypesI18N,
  ({ one }) => ({
    roleType: one(peopleRoleTypes, {
      fields: [peopleRoleTypesI18N.dataId],
      references: [peopleRoleTypes.id],
    }),
    locale: one(locales, {
      fields: [peopleRoleTypesI18N.locale],
      references: [locales.code],
    }),
  }),
);
