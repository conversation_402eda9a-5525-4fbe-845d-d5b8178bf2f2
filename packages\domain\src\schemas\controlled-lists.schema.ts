import * as Schema from 'effect/Schema';
import {
  CollectionViewSchema,
  LimitParamSchema,
  LocaleSchema,
} from './query.schema';

// Define available controllable entities
export const ControlledListsSchema = Schema.Union(
  Schema.Literal('applicationSectors'),
  Schema.Literal('documentationCategories'),
  Schema.Literal('equipmentCategories'),
  Schema.Literal('equipmentStatuses'),
  Schema.Literal('equipmentTypes'),
  Schema.Literal('excellenceHubs'),
  Schema.Literal('fundingProjectTypes'),
  Schema.Literal('fundingProjectIdentifierTypes'),
  Schema.Literal('innovationLabs'),
  Schema.Literal('infrastructureStatuses'),
  Schema.Literal('infrastructureTypes'),
  Schema.Literal('institutionTypes'),
  Schema.Literal('mediaTypes'),
  Schema.Literal('peopleRoleTypes'),
  Schema.Literal('roomCategories'),
  Schema.Literal('researchFields'),
  Schema.Literal('techniques'),
  Schema.Literal('unitTypes'),
  Schema.Literal('visibilities'),
);

// Schema for controlled list query parameters
export const ControlledListsQuerySchema = Schema.Struct({
  entities: Schema.Array(ControlledListsSchema).pipe(
    Schema.minItems(1, {
      message: () => 'At least one entity must be specified',
    }),
    Schema.maxItems(19, {
      message: () => 'Maximum 19 entities allowed per request',
    }),
  ),
  view: CollectionViewSchema,
  locale: LocaleSchema,
  limit: LimitParamSchema,
});

export const DbControlledListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.NullOr(Schema.String),
  locale: Schema.String,
});

// Schema for individual controlled list item
export const ControlledListItemSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// Schema for the response
export const ControlledListsResponseSchema = Schema.Record({
  key: ControlledListsSchema,
  value: Schema.Array(ControlledListItemSchema),
});

export type ControlledList = Schema.Schema.Type<typeof DbControlledListSchema>;

export type ControlledListKey = Schema.Schema.Type<
  typeof ControlledListsSchema
>;
export type ControlledListsQuery = Schema.Schema.Type<
  typeof ControlledListsQuerySchema
>;
export type ControlledListItem = Schema.Schema.Type<
  typeof ControlledListItemSchema
>;
export type ControlledListsResponse = Schema.Schema.Type<
  typeof ControlledListsResponseSchema
>;
