import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InstitutionsRuntime } from '@/infrastructure/runtimes/institutions.runtime';
import { InstitutionsServiceLive } from '@/infrastructure/services/institutions.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateEstablishmentSchema
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

const institutionsRoute = new Hono();

institutionsRoute.get('/', async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.getAllInstitutions();
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.get('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.getInstitutionById(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.post('/', effectValidator('json', CreateEstablishmentSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.createInstitution(body);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.put('/:id', effectValidator('json', UpdateEstablishmentSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.updateInstitution({ id, ...body });
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.delete('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.deleteInstitution(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Institution deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { institutionsRoute };
