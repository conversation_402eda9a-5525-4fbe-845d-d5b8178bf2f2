import { handleEffectError } from '@/api/v2/utils/error-handler';
import { EstablishmentsRuntime } from '@/infrastructure/runtimes/establishments.runtime';
import { EstablishmentsServiceLive } from '@/infrastructure/services/establishments.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateEstablishmentSchema,
    UpdateEstablishmentSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

const establishmentsRouter = new Hono();

establishmentsRouter.get('/', async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* EstablishmentsServiceLive;
        return yield* svc.getAllEstablishments();
    });
    const result = await EstablishmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

establishmentsRouter.get('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* EstablishmentsServiceLive;
        return yield* svc.getEstablishmentById(id);
    });
    const result = await EstablishmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

establishmentsRouter.post('/', effectValidator('json', CreateEstablishmentSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* EstablishmentsServiceLive;
        return yield* svc.createEstablishment(body);
    });
    const result = await EstablishmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

establishmentsRouter.put('/:id', effectValidator('json', UpdateEstablishmentSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* EstablishmentsServiceLive;
        return yield* svc.updateEstablishment({ id, ...body });
    });
    const result = await EstablishmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

establishmentsRouter.delete('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* EstablishmentsServiceLive;
        return yield* svc.deleteEstablishment(id);
    });
    const result = await EstablishmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Establishment deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { establishmentsRouter };
