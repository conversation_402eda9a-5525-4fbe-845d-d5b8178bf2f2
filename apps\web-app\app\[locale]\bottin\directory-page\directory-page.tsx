'use client';

import { selectEntityColumns } from '@/app/[locale]/bottin/directory-page/columns';
import { EntityPageDirectoryHeader } from '@/components/entity-page/entity-page-header-directory';
import { ResourceList } from '@/components/list-resource/list-resource';
import { RouteGuard } from '@/components/permissions/route-guard';
import { initialColumnVisibility } from '@/constants/bottin/entity';
import { useGetEntitiesList } from '@/hooks/bottin/useGetEntitiesList';
import { extractDirectoryAddPathnames } from '@/i18n/i18n.helpers';
import { type DirectoryEntityKey, pathnames } from '@/i18n/settings';
import { useAppStore } from '@/providers/app-store-provider';
import type { SupportedLocale } from '@/types/locale';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

type DirectoryElementsPageProps = {
  directoryEntity: DirectoryEntityKey;
  locale: SupportedLocale;
};

const entityResourceTranslationKeyMap: Record<DirectoryEntityKey, string> = {
  buildings: 'building',
  campus: 'campus',
  establishments: 'establishment',
  financingProjects: 'financingProject',
  manufacturers: 'manufacturer',
  people: 'people',
  rooms: 'room',
  units: 'unit',
} as const;

export const DirectoryElementsPage = ({
  directoryEntity,
  locale,
}: DirectoryElementsPageProps) => {
  const t = useTranslations('common');
  const [{ pageIndex, pageSize }, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const queryParams = useAppStore((state) => state.queryParams);
  const {
    data: entities,
    error,
    isLoading,
    status,
  } = useGetEntitiesList({
    directoryEntity,
    pageParam: pageIndex,
    params: {
      lang: locale,
      limit: pageSize.toString(),
    },
    queryParams,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <Whenever queryParams change we need to update the pagination>
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [queryParams]);

  const directoryAddPathnames = extractDirectoryAddPathnames(pathnames);
  const addResourceHref = directoryAddPathnames[directoryEntity];

  if (status === 'error') {
    return (
      <div>
        Error {directoryEntity} Page: {error.message}
      </div>
    );
  }

  return (
    <RouteGuard operation="read" resource="directory">
      <div className="w-full gap-y-6">
        <EntityPageDirectoryHeader
          addResourceHref={addResourceHref}
          addResourceLabel={`${t('addResource', { resource: t(`resources.${entityResourceTranslationKeyMap[directoryEntity]}`).toLowerCase() })}`}
          countLabel={t(`${directoryEntity}Count`, {
            count: entities?.count ?? 0,
          })}
          resource={directoryEntity}
          searchPlaceholder={t('search')}
          viewMode={'table'}
        />
        <div>
          <ResourceList
            columns={selectEntityColumns(directoryEntity)}
            data={entities?.data ?? []}
            initialColumnVisibility={initialColumnVisibility[directoryEntity]}
            isLoading={isLoading}
            onPageChange={setPagination}
            pageIndex={pageIndex}
            pageSize={pageSize}
            resourceName={`directory.${directoryEntity}`}
            totalCount={entities?.count ?? 0}
          />
        </div>
      </div>
    </RouteGuard>
  );
};
