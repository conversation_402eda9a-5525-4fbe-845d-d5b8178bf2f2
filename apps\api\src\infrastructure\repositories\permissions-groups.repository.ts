import { ConfigLive } from '@/infrastructure/config/config.live';
import type {
  DbPermissionGroupInput,
  DbPermissionGroupPermissionInput,
} from '@rie/db-schema/entity-types';
import {
  permissionGroupPermissions,
  permissionGroups,
} from '@rie/db-schema/schemas';
import { Database } from '@rie/postgres-db';
import { Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class PermissionsGroupsRepositoryLive extends Effect.Service<PermissionsGroupsRepositoryLive>()(
  'PermissionsGroupsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      /**
       * Find all permission groups
       */
      const findAllPermissionsGroups = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.permissionGroups.findMany({
            columns: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      /**
       * Find a permission group by ID
       */
      const findPermissionsGroupById = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client.query.permissionGroups.findFirst({
              where: eq(permissionGroups.id, id),
              columns: {
                id: true,
                name: true,
                description: true,
                createdAt: true,
                updatedAt: true,
              },
              with: {
                permissionGroupPermissions: {
                  columns: {
                    groupId: true,
                    permissionId: true,
                  },
                  with: {
                    permission: {
                      columns: {
                        id: true,
                        domain: true,
                        action: true,
                      },
                    },
                    group: {
                      columns: {
                        id: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      /**
       * Check if a permission group with the given name already exists
       */
      const checkPermissionsGroupExists = dbClient.makeQuery(
        (execute, name: string) => {
          return execute((client) =>
            client.query.permissionGroups.findFirst({
              where: eq(permissionGroups.name, name),
              columns: {
                id: true,
              },
            }),
          );
        },
      );

      /**
       * Create a new permission group
       * This uses a transaction to ensure all related data is created atomically
       */
      interface CreatePermissionsGroupParams {
        groupData: DbPermissionGroupInput;
        permissionIds: readonly string[];
      }
      const createPermissionsGroup = ({
        groupData,
        permissionIds,
      }: CreatePermissionsGroupParams) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Create the permission group
            const [group] = yield* tx((client) =>
              client.insert(permissionGroups).values(groupData).returning({
                id: permissionGroups.id,
                name: permissionGroups.name,
                description: permissionGroups.description,
                createdAt: permissionGroups.createdAt,
                updatedAt: permissionGroups.updatedAt,
              }),
            );

            // 2. Add permissions if provided
            if (permissionIds.length > 0) {
              const groupPermissions: DbPermissionGroupPermissionInput[] =
                permissionIds.map((permissionId) => ({
                  groupId: group.id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(permissionGroupPermissions)
                  .values(groupPermissions),
              );
            }

            return group;
          });
        });
      };

      /**
       * Update a permission group
       */
      interface UpdatePermissionsGroupParams
        extends CreatePermissionsGroupParams {
        id: string;
      }
      const updatePermissionsGroup = ({
        id,
        groupData,
        permissionIds,
      }: UpdatePermissionsGroupParams) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Update the permission group
            const [group] = yield* tx((client) =>
              client
                .update(permissionGroups)
                .set({
                  name: groupData.name,
                  description: groupData.description,
                  updatedAt: new Date().toISOString(),
                })
                .where(eq(permissionGroups.id, id))
                .returning({
                  id: permissionGroups.id,
                  name: permissionGroups.name,
                  description: permissionGroups.description,
                  createdAt: permissionGroups.createdAt,
                  updatedAt: permissionGroups.updatedAt,
                }),
            );

            // 3. Add permissions if provided
            if (permissionIds.length > 0) {
              yield* tx((client) =>
                client
                  .delete(permissionGroupPermissions)
                  .where(eq(permissionGroupPermissions.groupId, id)),
              );

              const groupPermissions: DbPermissionGroupPermissionInput[] =
                permissionIds.map((permissionId) => ({
                  groupId: group.id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(permissionGroupPermissions)
                  .values(groupPermissions),
              );
            }

            return group;
          });
        });
      };

      /**
       * Delete a permission group
       * This will cascade delete all related data due to foreign key constraints
       */
      const deletePermissionsGroup = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client
              .delete(permissionGroups)
              .where(eq(permissionGroups.id, id))
              .returning({ id: permissionGroups.id }),
          );
        },
      );

      return {
        findAllPermissionsGroups,
        findPermissionsGroupById,
        checkPermissionsGroupExists,
        createPermissionsGroup,
        updatePermissionsGroup,
        deletePermissionsGroup,
      } as const;
    }),
  },
) {}
