import * as Schema from 'effect/Schema';
import { descriptionSchema } from './base.schema';

// — Translation shape
export const FundingProjectTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(descriptionSchema),
  otherNames: Schema.optional(Schema.String),
  acronyms: Schema.optional(Schema.String),
});

export type FundingProjectTranslation = Schema.Schema.Type<typeof FundingProjectTranslationSchema>;

// — Full FundingProject shape
export const FundingProjectSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  holderId: Schema.String,                       // required - references people
  typeId: Schema.String,                         // required - references funding project types
  fciId: Schema.optional(Schema.String),        // nullable
  synchroId: Schema.String,                      // required
  obtainingYear: Schema.Number,                  // required
  endDate: Schema.optional(Schema.String),      // nullable date
  translations: Schema.Array(FundingProjectTranslationSchema), // at least []
  createdAt: Schema.String,                      // ISO timestamp
  updatedAt: Schema.String,                      // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),   // nullable
});

export type FundingProject = Schema.Schema.Type<typeof FundingProjectSchema>;

// — Input schemas for API
export const CreateFundingProjectSchema = Schema.Struct({
  holderId: Schema.String,
  typeId: Schema.String,
  fciId: Schema.optional(Schema.String),
  synchroId: Schema.String,
  obtainingYear: Schema.Number,
  endDate: Schema.optional(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  ),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateFundingProjectPayload = Schema.Schema.Type<typeof CreateFundingProjectSchema>;

export const UpdateFundingProjectSchema = Schema.Struct({
  holderId: Schema.optional(Schema.String),
  typeId: Schema.optional(Schema.String),
  fciId: Schema.optional(Schema.String),
  synchroId: Schema.optional(Schema.String),
  obtainingYear: Schema.optional(Schema.Number),
  endDate: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  )),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateFundingProjectPayload = Schema.Schema.Type<typeof UpdateFundingProjectSchema> & { id: string };

// — Database schema (for serializers)
export const DbFundingProjectSchema = FundingProjectSchema;

// — Item schema (for serializers)
export const FundingProjectItemSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

export type FundingProjectItem = Schema.Schema.Type<typeof FundingProjectItemSchema>;

// — Response schemas
export const FundingProjectResponseSchema = FundingProjectSchema;
export const FundingProjectListResponseSchema = Schema.Array(FundingProjectResponseSchema);

export type FundingProjectResponse = Schema.Schema.Type<typeof FundingProjectResponseSchema>;
export type FundingProjectListResponse = Schema.Schema.Type<typeof FundingProjectListResponseSchema>;
