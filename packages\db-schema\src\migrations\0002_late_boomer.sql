CREATE TYPE "public"."address_types" AS ENUM('campus', 'civic');--> statement-breakpoint
CREATE TYPE "public"."jurisdiction_types" AS ENUM('institution', 'unit');--> statement-breakpoint
CREATE TABLE "addresses" (
	"id" text PRIMARY KEY NOT NULL,
	"address_type" "address_types" NOT NULL,
	"campus_address_id" text,
	"civic_address_id" text
);
--> statement-breakpoint
CREATE TABLE "campus_addresses" (
	"id" text PRIMARY KEY NOT NULL,
	"room_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "civic_addresses" (
	"id" text PRIMARY KEY NOT NULL,
	"street1" text NOT NULL,
	"street2" text,
	"city" text NOT NULL,
	"state" text NOT NULL,
	"postal_code" text NOT NULL,
	"country_code" text NOT NULL,
	"place_id" text NOT NULL,
	"lat" text,
	"lon" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "application_sectors" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "application_sectors_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "buildings" (
	"id" text PRIMARY KEY NOT NULL,
	"campus_id" text,
	"civic_address_id" text,
	"sad_id" text,
	"di_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "buildings_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"other_names" text
);
--> statement-breakpoint
CREATE TABLE "campuses" (
	"id" text PRIMARY KEY NOT NULL,
	"sad_id" text,
	"institution_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "campuses_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text
);
--> statement-breakpoint
CREATE TABLE "documentation_categories" (
	"id" text PRIMARY KEY NOT NULL,
	"uid" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "documentation_categories_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_application_sectors" (
	"equipment_id" text NOT NULL,
	"application_sector_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_categories" (
	"equipment_id" text NOT NULL,
	"category_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_dimensions" (
	"id" text PRIMARY KEY NOT NULL,
	"equipment_id" text NOT NULL,
	"value" double precision NOT NULL,
	"unit_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_documents" (
	"id" text PRIMARY KEY NOT NULL,
	"equipment_id" text NOT NULL,
	"document_id" text NOT NULL,
	"category_id" text
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_excellence_hubs" (
	"equipment_id" text NOT NULL,
	"excellence_hub_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_funding_projects" (
	"equipment_id" text NOT NULL,
	"funding_project_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_lifecycles" (
	"id" text PRIMARY KEY NOT NULL,
	"equipment_id" text NOT NULL,
	"value" integer NOT NULL,
	"time_unit_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_maintenance_frequency" (
	"equipment_id" text NOT NULL,
	"value" double precision NOT NULL,
	"time_unit_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_measurement_precisions" (
	"equipment_id" text NOT NULL,
	"equipment_precision_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_media" (
	"equipment_id" text NOT NULL,
	"media_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_operational_managers" (
	"equipment_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_relationships" (
	"equipment_id" text NOT NULL,
	"related_equipment_id" text NOT NULL,
	"relationship_type_id" text NOT NULL,
	CONSTRAINT "equipment_associated_relationships_equipment_id_related_equipment_id_relationship_type_id_pk" PRIMARY KEY("equipment_id","related_equipment_id","relationship_type_id")
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_repairers" (
	"equipment_id" text NOT NULL,
	"vendor_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_research_fields" (
	"equipment_id" text NOT NULL,
	"research_field_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_retailers" (
	"equipment_id" text,
	"retailer_id" text
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_sst_managers" (
	"equipment_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_associated_techniques" (
	"equipment_id" text NOT NULL,
	"technique_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_categories" (
	"id" text PRIMARY KEY NOT NULL,
	"uid" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_categories_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_category_parents" (
	"child_id" text NOT NULL,
	"parent_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "equipment_measurement_precisions" (
	"id" text PRIMARY KEY NOT NULL,
	"value" double precision NOT NULL,
	"measurement_unit_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_measurement_units" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_measurement_units_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_relationship_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_relationship_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_relationships" (
	"id" text PRIMARY KEY NOT NULL,
	"type_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_relationships_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_statuses" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_statuses_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipment_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipment_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "equipments" (
	"id" text PRIMARY KEY NOT NULL,
	"guid_id" text,
	"campus_address_id" text,
	"is_campus_address_confidential" boolean DEFAULT false,
	"model" text,
	"serial_number" text,
	"homologation_number" text,
	"inventory_number" text,
	"doi" text,
	"use_in_clinical_trial" boolean DEFAULT false NOT NULL,
	"is_hidden" boolean DEFAULT false NOT NULL,
	"type_id" text NOT NULL,
	"status_id" text,
	"working_percentage" double precision,
	"monetary_cost" double precision,
	"in_kind_cost" double precision,
	"manufacture_year" integer,
	"acquisition_date" date,
	"installation_date" date,
	"decommissioning_date" date,
	"scientific_manager_id" text,
	"manufacturer_id" text,
	"supplier_id" text,
	"infrastructure_id" text,
	"is_featured" boolean DEFAULT false,
	"institution_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "equipments_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"specification" text,
	"usage_context" text,
	"risk" text,
	"comment" text
);
--> statement-breakpoint
CREATE TABLE "time_units" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "time_units_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"symbol" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "excellence_hubs" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "excellence_hubs_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_associated_identifiers" (
	"funding_project_id" text NOT NULL,
	"identifier_type_id" text NOT NULL,
	"identifier" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_associated_people" (
	"funding_project_id" text NOT NULL,
	"person_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_identifier_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_identifier_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_infrastructures" (
	"funding_project_id" text NOT NULL,
	"infrastructure_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_project_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text NOT NULL,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "funding_projects" (
	"id" text PRIMARY KEY NOT NULL,
	"holder_id" text NOT NULL,
	"type_id" text NOT NULL,
	"fci_id" text,
	"synchro_id" text NOT NULL,
	"obtaining_year" integer NOT NULL,
	"end_date" date,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "funding_projects_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text NOT NULL,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "guids" (
	"id" text PRIMARY KEY NOT NULL,
	"uuid" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text,
	CONSTRAINT "guids_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE "infrastructure_associated_innovation_labs" (
	"infrastructure_id" text NOT NULL,
	"innovation_lab_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "infrastructure_associated_operational_managers" (
	"infrastructure_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "infrastructure_associated_people" (
	"infrastructure_id" text NOT NULL,
	"person_id" text NOT NULL,
	"role_type_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "infrastructure_associated_scientific_managers" (
	"id" text PRIMARY KEY NOT NULL,
	"infrastructure_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "infrastructure_associated_sst_managers" (
	"infrastructure_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "infrastructure_role_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "infrastructure_statuses" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "infrastructure_statuses_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "infrastructure_types" (
	"id" text PRIMARY KEY NOT NULL,
	"uid" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "infrastructure_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "infrastructures" (
	"id" text PRIMARY KEY NOT NULL,
	"guid_id" text NOT NULL,
	"type_id" text NOT NULL,
	"address_id" text,
	"status_id" text NOT NULL,
	"website" text,
	"is_featured" boolean,
	"visibility_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "infrastructures_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"other_names" text,
	"acronyms" text
);
--> statement-breakpoint
CREATE TABLE "innovation_labs" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "innovation_labs_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "institution_associated_people" (
	"id" text PRIMARY KEY NOT NULL,
	"institution_id" text NOT NULL,
	"person_id" text NOT NULL,
	"role_type_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "institution_associated_units" (
	"institution_id" text NOT NULL,
	"unit_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "institution_role_types" (
	"id" text PRIMARY KEY NOT NULL,
	"uid" text
);
--> statement-breakpoint
CREATE TABLE "institution_role_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text
);
--> statement-breakpoint
CREATE TABLE "institution_types" (
	"id" text PRIMARY KEY NOT NULL,
	"uid" text
);
--> statement-breakpoint
CREATE TABLE "institution_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "institutions" (
	"id" text PRIMARY KEY NOT NULL,
	"guid_id" text,
	"type_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "institutions_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"acronyms" text,
	"other_names" text
);
--> statement-breakpoint
CREATE TABLE "locales" (
	"code" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "locales_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text
);
--> statement-breakpoint
CREATE TABLE "media" (
	"id" text PRIMARY KEY NOT NULL,
	"filename" text NOT NULL,
	"mime_type" text NOT NULL,
	"filesize" integer,
	"path" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "media_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"title" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "media_types" (
	"id" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "media_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "peoples" (
	"id" text PRIMARY KEY NOT NULL,
	"guid_id" text,
	"uid" text,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"user_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "people_associated_emails" (
	"id" text PRIMARY KEY NOT NULL,
	"person_id" text NOT NULL,
	"address" text NOT NULL,
	CONSTRAINT "people_associated_emails_address_unique" UNIQUE("address")
);
--> statement-breakpoint
CREATE TABLE "people_associated_media" (
	"media_id" text NOT NULL,
	"person_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "people_associated_phones" (
	"id" text PRIMARY KEY NOT NULL,
	"person_id" text NOT NULL,
	"phone_number" text
);
--> statement-breakpoint
CREATE TABLE "people_role_types" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "people_role_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text
);
--> statement-breakpoint
CREATE TABLE "research_fields" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "research_fields_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "room_associated_categories" (
	"room_id" text NOT NULL,
	"category_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "room_categories" (
	"id" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "room_categories_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "rooms" (
	"id" text PRIMARY KEY NOT NULL,
	"number" text NOT NULL,
	"area" double precision,
	"floor_load" double precision,
	"building_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "service_contracts" (
	"id" text PRIMARY KEY NOT NULL,
	"equipment_id" text NOT NULL,
	"vendor_id" text NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date,
	"monetary_cost" double precision,
	"in_kind_cost" double precision,
	"comment" text,
	"visibility_id" text NOT NULL,
	"number" text,
	"is_renewable" boolean DEFAULT false,
	"has_parts" boolean DEFAULT false,
	"parts_count" integer,
	"has_workforce" boolean DEFAULT false,
	"hour_bank" integer,
	"has_maintenance" boolean DEFAULT false,
	"maintenance_count" integer,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "service_contracts_document" (
	"id" text PRIMARY KEY NOT NULL,
	"service_contract_id" text NOT NULL,
	"document_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "service_contracts_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "service_offers" (
	"id" text PRIMARY KEY NOT NULL,
	"address" text,
	"is_for_clinical_research" boolean DEFAULT false NOT NULL,
	"highlight_service" boolean DEFAULT false NOT NULL,
	"created_by" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "service_offers_equipments" (
	"service_offer_id" text NOT NULL,
	"equipment_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "service_offers_i18n" (
	"data_id" text NOT NULL,
	"id" text PRIMARY KEY NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"service_conditions" text
);
--> statement-breakpoint
CREATE TABLE "techniques" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "techniques_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "unit_associated_buildings" (
	"unit_id" text NOT NULL,
	"building_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "unit_associated_jurisdictions" (
	"unit_id" text NOT NULL,
	"unit_parent_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "unit_associated_people" (
	"unit_id" text NOT NULL,
	"person_id" text NOT NULL,
	"role_type_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "unit_parents" (
	"id" text PRIMARY KEY NOT NULL,
	"type" "jurisdiction_types" NOT NULL,
	"institution_id" text,
	"unit_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "unit_types" (
	"id" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "unit_types_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "units" (
	"id" text PRIMARY KEY NOT NULL,
	"guid_id" text NOT NULL,
	"type_id" text NOT NULL,
	"parent_id" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "units_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text,
	"other_names" text,
	"acronyms" text
);
--> statement-breakpoint
CREATE TABLE "vendor_associated_contacts" (
	"vendor_id" text NOT NULL,
	"vendor_contact_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vendor_contacts" (
	"id" text PRIMARY KEY NOT NULL,
	"phone_number" text,
	"email" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "vendor_contacts_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"description" text
);
--> statement-breakpoint
CREATE TABLE "vendors" (
	"id" text PRIMARY KEY NOT NULL,
	"start_date" date,
	"end_date" date,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "vendors_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"website" text,
	"description" text,
	"other_names" text
);
--> statement-breakpoint
CREATE TABLE "visibilities" (
	"id" text PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"modified_by" text
);
--> statement-breakpoint
CREATE TABLE "visibilities_i18n" (
	"id" text PRIMARY KEY NOT NULL,
	"data_id" text NOT NULL,
	"locale" text NOT NULL,
	"name" text,
	"description" text
);
--> statement-breakpoint
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_campus_address_id_campus_addresses_id_fk" FOREIGN KEY ("campus_address_id") REFERENCES "public"."campus_addresses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_civic_address_id_civic_addresses_id_fk" FOREIGN KEY ("civic_address_id") REFERENCES "public"."civic_addresses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "campus_addresses" ADD CONSTRAINT "campus_addresses_room_id_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "application_sectors" ADD CONSTRAINT "application_sectors_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "application_sectors_i18n" ADD CONSTRAINT "application_sectors_i18n_data_id_application_sectors_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."application_sectors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "application_sectors_i18n" ADD CONSTRAINT "application_sectors_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "buildings" ADD CONSTRAINT "buildings_campus_id_campuses_id_fk" FOREIGN KEY ("campus_id") REFERENCES "public"."campuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "buildings" ADD CONSTRAINT "buildings_civic_address_id_civic_addresses_id_fk" FOREIGN KEY ("civic_address_id") REFERENCES "public"."civic_addresses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "buildings" ADD CONSTRAINT "buildings_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "buildings_i18n" ADD CONSTRAINT "buildings_i18n_data_id_buildings_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."buildings"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "buildings_i18n" ADD CONSTRAINT "buildings_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "campuses" ADD CONSTRAINT "campuses_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "campuses" ADD CONSTRAINT "campuses_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "campuses_i18n" ADD CONSTRAINT "campuses_i18n_data_id_campuses_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."campuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "campuses_i18n" ADD CONSTRAINT "campuses_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "documentation_categories" ADD CONSTRAINT "documentation_categories_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "documentation_categories_i18n" ADD CONSTRAINT "documentation_categories_i18n_data_id_documentation_categories_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."documentation_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "documentation_categories_i18n" ADD CONSTRAINT "documentation_categories_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_application_sectors" ADD CONSTRAINT "equipment_associated_application_sectors_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_application_sectors" ADD CONSTRAINT "equipment_associated_application_sectors_application_sector_id_application_sectors_id_fk" FOREIGN KEY ("application_sector_id") REFERENCES "public"."application_sectors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_categories" ADD CONSTRAINT "equipment_associated_categories_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_categories" ADD CONSTRAINT "equipment_associated_categories_category_id_equipment_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."equipment_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_dimensions" ADD CONSTRAINT "equipment_associated_dimensions_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_dimensions" ADD CONSTRAINT "equipment_associated_dimensions_unit_id_equipment_measurement_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."equipment_measurement_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_documents" ADD CONSTRAINT "equipment_associated_documents_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_documents" ADD CONSTRAINT "equipment_associated_documents_document_id_media_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_documents" ADD CONSTRAINT "equipment_associated_documents_category_id_documentation_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."documentation_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_excellence_hubs" ADD CONSTRAINT "equipment_associated_excellence_hubs_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_excellence_hubs" ADD CONSTRAINT "equipment_associated_excellence_hubs_excellence_hub_id_excellence_hubs_id_fk" FOREIGN KEY ("excellence_hub_id") REFERENCES "public"."excellence_hubs"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_funding_projects" ADD CONSTRAINT "equipment_associated_funding_projects_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_funding_projects" ADD CONSTRAINT "equipment_associated_funding_projects_funding_project_id_funding_projects_id_fk" FOREIGN KEY ("funding_project_id") REFERENCES "public"."funding_projects"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_lifecycles" ADD CONSTRAINT "equipment_associated_lifecycles_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_lifecycles" ADD CONSTRAINT "equipment_associated_lifecycles_time_unit_id_time_units_id_fk" FOREIGN KEY ("time_unit_id") REFERENCES "public"."time_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_lifecycles" ADD CONSTRAINT "equipment_associated_lifecycles_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_associated_maintenance_frequency" ADD CONSTRAINT "equipment_associated_maintenance_frequency_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_maintenance_frequency" ADD CONSTRAINT "equipment_associated_maintenance_frequency_time_unit_id_time_units_id_fk" FOREIGN KEY ("time_unit_id") REFERENCES "public"."time_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_measurement_precisions" ADD CONSTRAINT "equipment_associated_measurement_precisions_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_measurement_precisions" ADD CONSTRAINT "equipment_associated_measurement_precisions_equipment_precision_id_equipment_measurement_precisions_id_fk" FOREIGN KEY ("equipment_precision_id") REFERENCES "public"."equipment_measurement_precisions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_media" ADD CONSTRAINT "equipment_associated_media_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_media" ADD CONSTRAINT "equipment_associated_media_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_operational_managers" ADD CONSTRAINT "equipment_associated_operational_managers_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_operational_managers" ADD CONSTRAINT "equipment_associated_operational_managers_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_relationships" ADD CONSTRAINT "equipment_associated_relationships_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_relationships" ADD CONSTRAINT "equipment_associated_relationships_related_equipment_id_equipments_id_fk" FOREIGN KEY ("related_equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_relationships" ADD CONSTRAINT "equipment_associated_relationships_relationship_type_id_equipment_relationship_types_id_fk" FOREIGN KEY ("relationship_type_id") REFERENCES "public"."equipment_relationship_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_repairers" ADD CONSTRAINT "equipment_associated_repairers_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_repairers" ADD CONSTRAINT "equipment_associated_repairers_vendor_id_vendors_id_fk" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_research_fields" ADD CONSTRAINT "equipment_associated_research_fields_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_research_fields" ADD CONSTRAINT "equipment_associated_research_fields_research_field_id_research_fields_id_fk" FOREIGN KEY ("research_field_id") REFERENCES "public"."research_fields"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_retailers" ADD CONSTRAINT "equipment_associated_retailers_equipment_id_vendors_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_retailers" ADD CONSTRAINT "equipment_associated_retailers_retailer_id_vendors_id_fk" FOREIGN KEY ("retailer_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_sst_managers" ADD CONSTRAINT "equipment_associated_sst_managers_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_sst_managers" ADD CONSTRAINT "equipment_associated_sst_managers_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_techniques" ADD CONSTRAINT "equipment_associated_techniques_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_associated_techniques" ADD CONSTRAINT "equipment_associated_techniques_technique_id_techniques_id_fk" FOREIGN KEY ("technique_id") REFERENCES "public"."techniques"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_categories" ADD CONSTRAINT "equipment_categories_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_categories_i18n" ADD CONSTRAINT "equipment_categories_i18n_data_id_equipment_categories_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_categories_i18n" ADD CONSTRAINT "equipment_categories_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_category_parents" ADD CONSTRAINT "equipment_category_parents_child_id_equipment_categories_id_fk" FOREIGN KEY ("child_id") REFERENCES "public"."equipment_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_category_parents" ADD CONSTRAINT "equipment_category_parents_parent_id_equipment_categories_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."equipment_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_measurement_precisions" ADD CONSTRAINT "equipment_measurement_precisions_measurement_unit_id_equipment_measurement_units_id_fk" FOREIGN KEY ("measurement_unit_id") REFERENCES "public"."equipment_measurement_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_measurement_precisions" ADD CONSTRAINT "equipment_measurement_precisions_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_measurement_units" ADD CONSTRAINT "equipment_measurement_units_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_measurement_units_i18n" ADD CONSTRAINT "equipment_measurement_units_i18n_data_id_equipment_measurement_units_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_measurement_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_measurement_units_i18n" ADD CONSTRAINT "equipment_measurement_units_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_relationship_types" ADD CONSTRAINT "equipment_relationship_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_relationship_types_i18n" ADD CONSTRAINT "equipment_relationship_types_i18n_data_id_equipment_relationship_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_relationship_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_relationship_types_i18n" ADD CONSTRAINT "equipment_relationship_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_relationships" ADD CONSTRAINT "equipment_relationships_type_id_equipment_relationship_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."equipment_relationship_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_relationships" ADD CONSTRAINT "equipment_relationships_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_relationships_i18n" ADD CONSTRAINT "equipment_relationships_i18n_data_id_equipment_relationships_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_relationships"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_relationships_i18n" ADD CONSTRAINT "equipment_relationships_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_statuses" ADD CONSTRAINT "equipment_statuses_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_statuses_i18n" ADD CONSTRAINT "equipment_statuses_i18n_data_id_equipment_statuses_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_statuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_statuses_i18n" ADD CONSTRAINT "equipment_statuses_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_types" ADD CONSTRAINT "equipment_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipment_types_i18n" ADD CONSTRAINT "equipment_types_i18n_data_id_equipment_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipment_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipment_types_i18n" ADD CONSTRAINT "equipment_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_guid_id_guids_id_fk" FOREIGN KEY ("guid_id") REFERENCES "public"."guids"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_campus_address_id_campus_addresses_id_fk" FOREIGN KEY ("campus_address_id") REFERENCES "public"."campus_addresses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_type_id_equipment_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."equipment_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_status_id_equipment_statuses_id_fk" FOREIGN KEY ("status_id") REFERENCES "public"."equipment_statuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_scientific_manager_id_peoples_id_fk" FOREIGN KEY ("scientific_manager_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_manufacturer_id_vendors_id_fk" FOREIGN KEY ("manufacturer_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_supplier_id_vendors_id_fk" FOREIGN KEY ("supplier_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments" ADD CONSTRAINT "equipments_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "equipments_i18n" ADD CONSTRAINT "equipments_i18n_data_id_equipments_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "equipments_i18n" ADD CONSTRAINT "equipments_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "time_units" ADD CONSTRAINT "time_units_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "time_units_i18n" ADD CONSTRAINT "time_units_i18n_data_id_time_units_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."time_units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "time_units_i18n" ADD CONSTRAINT "time_units_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "excellence_hubs" ADD CONSTRAINT "excellence_hubs_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "excellence_hubs_i18n" ADD CONSTRAINT "excellence_hubs_i18n_data_id_excellence_hubs_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."excellence_hubs"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "excellence_hubs_i18n" ADD CONSTRAINT "excellence_hubs_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_associated_identifiers" ADD CONSTRAINT "funding_project_associated_identifiers_funding_project_id_funding_projects_id_fk" FOREIGN KEY ("funding_project_id") REFERENCES "public"."funding_projects"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_associated_identifiers" ADD CONSTRAINT "funding_project_associated_identifiers_identifier_type_id_funding_project_identifier_types_id_fk" FOREIGN KEY ("identifier_type_id") REFERENCES "public"."funding_project_identifier_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_associated_identifiers" ADD CONSTRAINT "funding_project_associated_identifiers_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_project_associated_people" ADD CONSTRAINT "funding_project_associated_people_funding_project_id_funding_projects_id_fk" FOREIGN KEY ("funding_project_id") REFERENCES "public"."funding_projects"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_associated_people" ADD CONSTRAINT "funding_project_associated_people_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_associated_people" ADD CONSTRAINT "funding_project_associated_people_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_project_identifier_types" ADD CONSTRAINT "funding_project_identifier_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_project_identifier_types_i18n" ADD CONSTRAINT "funding_project_identifier_types_i18n_data_id_funding_project_identifier_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."funding_project_identifier_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_identifier_types_i18n" ADD CONSTRAINT "funding_project_identifier_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_infrastructures" ADD CONSTRAINT "funding_project_infrastructures_funding_project_id_funding_projects_id_fk" FOREIGN KEY ("funding_project_id") REFERENCES "public"."funding_projects"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_infrastructures" ADD CONSTRAINT "funding_project_infrastructures_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_infrastructures" ADD CONSTRAINT "funding_project_infrastructures_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_project_types" ADD CONSTRAINT "funding_project_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_project_types_i18n" ADD CONSTRAINT "funding_project_types_i18n_data_id_funding_project_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."funding_project_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_project_types_i18n" ADD CONSTRAINT "funding_project_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_projects" ADD CONSTRAINT "funding_projects_holder_id_peoples_id_fk" FOREIGN KEY ("holder_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_projects" ADD CONSTRAINT "funding_projects_type_id_funding_project_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."funding_project_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_projects" ADD CONSTRAINT "funding_projects_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "funding_projects_i18n" ADD CONSTRAINT "funding_projects_i18n_data_id_funding_projects_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."funding_projects"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "funding_projects_i18n" ADD CONSTRAINT "funding_projects_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "guids" ADD CONSTRAINT "guids_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_innovation_labs" ADD CONSTRAINT "infrastructure_associated_innovation_labs_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_innovation_labs" ADD CONSTRAINT "infrastructure_associated_innovation_labs_innovation_lab_id_innovation_labs_id_fk" FOREIGN KEY ("innovation_lab_id") REFERENCES "public"."innovation_labs"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_operational_managers" ADD CONSTRAINT "infrastructure_associated_operational_managers_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_operational_managers" ADD CONSTRAINT "infrastructure_associated_operational_managers_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_people" ADD CONSTRAINT "infrastructure_associated_people_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_people" ADD CONSTRAINT "infrastructure_associated_people_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_people" ADD CONSTRAINT "infrastructure_associated_people_role_type_id_infrastructure_role_types_id_fk" FOREIGN KEY ("role_type_id") REFERENCES "public"."infrastructure_role_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_scientific_managers" ADD CONSTRAINT "infrastructure_associated_scientific_managers_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_scientific_managers" ADD CONSTRAINT "infrastructure_associated_scientific_managers_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_sst_managers" ADD CONSTRAINT "infrastructure_associated_sst_managers_infrastructure_id_infrastructures_id_fk" FOREIGN KEY ("infrastructure_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_associated_sst_managers" ADD CONSTRAINT "infrastructure_associated_sst_managers_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_role_types" ADD CONSTRAINT "infrastructure_role_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructure_statuses" ADD CONSTRAINT "infrastructure_statuses_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructure_statuses_i18n" ADD CONSTRAINT "infrastructure_statuses_i18n_data_id_infrastructure_statuses_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."infrastructure_statuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_statuses_i18n" ADD CONSTRAINT "infrastructure_statuses_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_types" ADD CONSTRAINT "infrastructure_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructure_types_i18n" ADD CONSTRAINT "infrastructure_types_i18n_data_id_infrastructure_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."infrastructure_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructure_types_i18n" ADD CONSTRAINT "infrastructure_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_guid_id_guids_id_fk" FOREIGN KEY ("guid_id") REFERENCES "public"."guids"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_type_id_infrastructure_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."infrastructure_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_address_id_addresses_id_fk" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_status_id_infrastructure_statuses_id_fk" FOREIGN KEY ("status_id") REFERENCES "public"."infrastructure_statuses"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_visibility_id_visibilities_id_fk" FOREIGN KEY ("visibility_id") REFERENCES "public"."visibilities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructures" ADD CONSTRAINT "infrastructures_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "infrastructures_i18n" ADD CONSTRAINT "infrastructures_i18n_data_id_infrastructures_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."infrastructures"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "infrastructures_i18n" ADD CONSTRAINT "infrastructures_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "innovation_labs" ADD CONSTRAINT "innovation_labs_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "innovation_labs_i18n" ADD CONSTRAINT "innovation_labs_i18n_data_id_innovation_labs_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."innovation_labs"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "innovation_labs_i18n" ADD CONSTRAINT "innovation_labs_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_associated_people" ADD CONSTRAINT "institution_associated_people_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_associated_people" ADD CONSTRAINT "institution_associated_people_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_associated_people" ADD CONSTRAINT "institution_associated_people_role_type_id_institution_role_types_id_fk" FOREIGN KEY ("role_type_id") REFERENCES "public"."institution_role_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_associated_units" ADD CONSTRAINT "institution_associated_units_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_associated_units" ADD CONSTRAINT "institution_associated_units_unit_id_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_role_types_i18n" ADD CONSTRAINT "institution_role_types_i18n_data_id_institution_role_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."institution_role_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_role_types_i18n" ADD CONSTRAINT "institution_role_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_types_i18n" ADD CONSTRAINT "institution_types_i18n_data_id_institution_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."institution_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institution_types_i18n" ADD CONSTRAINT "institution_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institutions" ADD CONSTRAINT "institutions_guid_id_guids_id_fk" FOREIGN KEY ("guid_id") REFERENCES "public"."guids"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institutions" ADD CONSTRAINT "institutions_type_id_institution_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."institution_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institutions" ADD CONSTRAINT "institutions_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "institutions_i18n" ADD CONSTRAINT "institutions_i18n_data_id_institutions_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institutions_i18n" ADD CONSTRAINT "institutions_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "locales_i18n" ADD CONSTRAINT "locales_i18n_data_id_locales_code_fk" FOREIGN KEY ("data_id") REFERENCES "public"."locales"("code") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "locales_i18n" ADD CONSTRAINT "locales_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "media" ADD CONSTRAINT "media_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "media_i18n" ADD CONSTRAINT "media_i18n_data_id_media_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "media_types_i18n" ADD CONSTRAINT "media_types_i18n_data_id_media_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."media_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "peoples" ADD CONSTRAINT "peoples_guid_id_guids_id_fk" FOREIGN KEY ("guid_id") REFERENCES "public"."guids"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "peoples" ADD CONSTRAINT "peoples_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "peoples" ADD CONSTRAINT "peoples_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "people_associated_emails" ADD CONSTRAINT "people_associated_emails_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "people_associated_media" ADD CONSTRAINT "people_associated_media_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "people_associated_media" ADD CONSTRAINT "people_associated_media_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "people_associated_phones" ADD CONSTRAINT "people_associated_phones_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "people_role_types" ADD CONSTRAINT "people_role_types_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "people_role_types_i18n" ADD CONSTRAINT "people_role_types_i18n_data_id_people_role_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."people_role_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "people_role_types_i18n" ADD CONSTRAINT "people_role_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "research_fields" ADD CONSTRAINT "research_fields_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "research_fields_i18n" ADD CONSTRAINT "research_fields_i18n_data_id_research_fields_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."research_fields"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "research_fields_i18n" ADD CONSTRAINT "research_fields_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "room_associated_categories" ADD CONSTRAINT "room_associated_categories_room_id_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."rooms"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "room_associated_categories" ADD CONSTRAINT "room_associated_categories_category_id_room_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."room_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "room_categories_i18n" ADD CONSTRAINT "room_categories_i18n_data_id_room_categories_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."room_categories"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "room_categories_i18n" ADD CONSTRAINT "room_categories_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "rooms" ADD CONSTRAINT "rooms_building_id_buildings_id_fk" FOREIGN KEY ("building_id") REFERENCES "public"."buildings"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "rooms" ADD CONSTRAINT "rooms_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_contracts" ADD CONSTRAINT "service_contracts_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts" ADD CONSTRAINT "service_contracts_vendor_id_vendors_id_fk" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts" ADD CONSTRAINT "service_contracts_visibility_id_visibilities_id_fk" FOREIGN KEY ("visibility_id") REFERENCES "public"."visibilities"("id") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts" ADD CONSTRAINT "service_contracts_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_contracts_document" ADD CONSTRAINT "service_contracts_document_service_contract_id_service_contracts_id_fk" FOREIGN KEY ("service_contract_id") REFERENCES "public"."service_contracts"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts_document" ADD CONSTRAINT "service_contracts_document_document_id_media_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts_i18n" ADD CONSTRAINT "service_contracts_i18n_data_id_service_contracts_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."service_contracts"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_contracts_i18n" ADD CONSTRAINT "service_contracts_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_offers" ADD CONSTRAINT "service_offers_address_addresses_id_fk" FOREIGN KEY ("address") REFERENCES "public"."addresses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_offers" ADD CONSTRAINT "service_offers_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_offers_equipments" ADD CONSTRAINT "service_offers_equipments_service_offer_id_service_offers_id_fk" FOREIGN KEY ("service_offer_id") REFERENCES "public"."service_offers"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_offers_equipments" ADD CONSTRAINT "service_offers_equipments_equipment_id_equipments_id_fk" FOREIGN KEY ("equipment_id") REFERENCES "public"."equipments"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service_offers_i18n" ADD CONSTRAINT "service_offers_i18n_data_id_service_offers_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."service_offers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_offers_i18n" ADD CONSTRAINT "service_offers_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "techniques" ADD CONSTRAINT "techniques_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "techniques_i18n" ADD CONSTRAINT "techniques_i18n_data_id_techniques_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."techniques"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "techniques_i18n" ADD CONSTRAINT "techniques_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_buildings" ADD CONSTRAINT "unit_associated_buildings_unit_id_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_buildings" ADD CONSTRAINT "unit_associated_buildings_building_id_buildings_id_fk" FOREIGN KEY ("building_id") REFERENCES "public"."buildings"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_jurisdictions" ADD CONSTRAINT "unit_associated_jurisdictions_unit_id_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_jurisdictions" ADD CONSTRAINT "unit_associated_jurisdictions_unit_parent_id_unit_parents_id_fk" FOREIGN KEY ("unit_parent_id") REFERENCES "public"."unit_parents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_people" ADD CONSTRAINT "unit_associated_people_unit_id_units_id_fk" FOREIGN KEY ("unit_id") REFERENCES "public"."units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_people" ADD CONSTRAINT "unit_associated_people_person_id_peoples_id_fk" FOREIGN KEY ("person_id") REFERENCES "public"."peoples"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_associated_people" ADD CONSTRAINT "unit_associated_people_role_type_id_people_role_types_id_fk" FOREIGN KEY ("role_type_id") REFERENCES "public"."people_role_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_parents" ADD CONSTRAINT "unit_parents_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_parents" ADD CONSTRAINT "unit_parents_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "unit_types_i18n" ADD CONSTRAINT "unit_types_i18n_data_id_unit_types_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."unit_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "unit_types_i18n" ADD CONSTRAINT "unit_types_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "units" ADD CONSTRAINT "units_guid_id_guids_id_fk" FOREIGN KEY ("guid_id") REFERENCES "public"."guids"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "units" ADD CONSTRAINT "units_type_id_unit_types_id_fk" FOREIGN KEY ("type_id") REFERENCES "public"."unit_types"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "units" ADD CONSTRAINT "units_parent_id_unit_parents_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."unit_parents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "units" ADD CONSTRAINT "units_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "units_i18n" ADD CONSTRAINT "units_i18n_data_id_units_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."units"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "units_i18n" ADD CONSTRAINT "units_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendor_associated_contacts" ADD CONSTRAINT "vendor_associated_contacts_vendor_id_vendors_id_fk" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendor_associated_contacts" ADD CONSTRAINT "vendor_associated_contacts_vendor_contact_id_vendor_contacts_id_fk" FOREIGN KEY ("vendor_contact_id") REFERENCES "public"."vendor_contacts"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendor_contacts" ADD CONSTRAINT "vendor_contacts_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vendor_contacts_i18n" ADD CONSTRAINT "vendor_contacts_i18n_data_id_vendor_contacts_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."vendor_contacts"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendor_contacts_i18n" ADD CONSTRAINT "vendor_contacts_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendors" ADD CONSTRAINT "vendors_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vendors_i18n" ADD CONSTRAINT "vendors_i18n_data_id_vendors_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."vendors"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "vendors_i18n" ADD CONSTRAINT "vendors_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "visibilities" ADD CONSTRAINT "visibilities_modified_by_users_id_fk" FOREIGN KEY ("modified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "visibilities_i18n" ADD CONSTRAINT "visibilities_i18n_data_id_visibilities_id_fk" FOREIGN KEY ("data_id") REFERENCES "public"."visibilities"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "visibilities_i18n" ADD CONSTRAINT "visibilities_i18n_locale_locales_code_fk" FOREIGN KEY ("locale") REFERENCES "public"."locales"("code") ON DELETE no action ON UPDATE cascade;