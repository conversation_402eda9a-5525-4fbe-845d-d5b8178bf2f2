import { equipmentAssociatedDocuments, locales, users } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const documentationCategories = pgTable(
  'documentation_categories',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    uid: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uidUnique: unique().on(table.uid),
    },
  ],
);

export const documentationCategoryRelations = relations(
  documentationCategories,
  ({ many }) => ({
    equipmentDocuments: many(equipmentAssociatedDocuments),
    translations: many(documentationCategoriesI18N),
  }),
);

export const documentationCategoriesI18N = pgTable(
  'documentation_categories_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => documentationCategories.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const documentationCategoriesI18NRelations = relations(
  documentationCategoriesI18N,
  ({ one }) => ({
    documentationCategory: one(documentationCategories, {
      fields: [documentationCategoriesI18N.dataId],
      references: [documentationCategories.id],
    }),
    locale: one(locales, {
      fields: [documentationCategoriesI18N.locale],
      references: [locales.code],
    }),
  }),
);
