import type {
  CreateEquipmentSchema,
  UpdateEquipmentSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

export class EquipmentsServiceLive extends Effect.Service<EquipmentsServiceLive>()(
  'EquipmentsServiceLive',
  {
    dependencies: [],
    effect: Effect.gen(function* () {

      const getAllEquipments = () =>
        Effect.gen(function* () {
          const equipments = yield* Effect.tryPromise({
            try: () =>
              db
                .select({
                  id: DbSchema.equipments.id,
                  guidId: DbSchema.equipments.guidId,
                  campusAddressId: DbSchema.equipments.campusAddressId,
                  isCampusAddressConfidential: DbSchema.equipments.isCampusAddressConfidential,
                  model: DbSchema.equipments.model,
                  serialNumber: DbSchema.equipments.serialNumber,
                  homologationNumber: DbSchema.equipments.homologationNumber,
                  inventoryNumber: DbSchema.equipments.inventoryNumber,
                  doi: DbSchema.equipments.doi,
                  useInClinicalTrial: DbSchema.equipments.useInClinicalTrial,
                  isHidden: DbSchema.equipments.isHidden,
                  typeId: DbSchema.equipments.typeId,
                  statusId: DbSchema.equipments.statusId,
                  workingPercentage: DbSchema.equipments.workingPercentage,
                  monetaryCost: DbSchema.equipments.monetaryCost,
                  inKindCost: DbSchema.equipments.inKindCost,
                  manufactureYear: DbSchema.equipments.manufactureYear,
                  acquisitionDate: DbSchema.equipments.acquisitionDate,
                  installationDate: DbSchema.equipments.installationDate,
                  decommissioningDate: DbSchema.equipments.decommissioningDate,
                  scientificManagerId: DbSchema.equipments.scientificManagerId,
                  manufacturerId: DbSchema.equipments.manufacturerId,
                  supplierId: DbSchema.equipments.supplierId,
                  infrastructureId: DbSchema.equipments.infrastructureId,
                  isFeatured: DbSchema.equipments.isFeatured,
                  institutionId: DbSchema.equipments.institutionId,
                  createdAt: DbSchema.equipments.createdAt,
                  updatedAt: DbSchema.equipments.updatedAt,
                  modifiedBy: DbSchema.equipments.modifiedBy,
                  translations: {
                    locale: DbSchema.equipmentsI18N.locale,
                    name: DbSchema.equipmentsI18N.name,
                    description: DbSchema.equipmentsI18N.description,
                    otherNames: DbSchema.equipmentsI18N.otherNames,
                    acronyms: DbSchema.equipmentsI18N.acronyms,
                  },
                })
                .from(DbSchema.equipments)
                .leftJoin(
                  DbSchema.equipmentsI18N,
                  eq(DbSchema.equipments.id, DbSchema.equipmentsI18N.dataId),
                ),
            catch: (error) => new Error(`Failed to fetch equipments: ${error}`),
          });

          // Group translations by equipment
          const groupedEquipments = equipments.reduce(
            (acc, row) => {
              const existing = acc.find((item) => item.id === row.id);
              if (existing) {
                if (row.translations.locale) {
                  existing.translations.push(row.translations);
                }
              } else {
                acc.push({
                  ...row,
                  translations: row.translations.locale ? [row.translations] : [],
                });
              }
              return acc;
            },
            [] as Array<Schema.Schema.Type<typeof EquipmentSchema>>,
          );

          return groupedEquipments;
        });

      const getEquipmentById = (id: string) =>
        Effect.gen(function* () {
          const equipment = yield* Effect.tryPromise({
            try: () =>
              db
                .select({
                  id: DbSchema.equipments.id,
                  guidId: DbSchema.equipments.guidId,
                  campusAddressId: DbSchema.equipments.campusAddressId,
                  isCampusAddressConfidential: DbSchema.equipments.isCampusAddressConfidential,
                  model: DbSchema.equipments.model,
                  serialNumber: DbSchema.equipments.serialNumber,
                  homologationNumber: DbSchema.equipments.homologationNumber,
                  inventoryNumber: DbSchema.equipments.inventoryNumber,
                  doi: DbSchema.equipments.doi,
                  useInClinicalTrial: DbSchema.equipments.useInClinicalTrial,
                  isHidden: DbSchema.equipments.isHidden,
                  typeId: DbSchema.equipments.typeId,
                  statusId: DbSchema.equipments.statusId,
                  workingPercentage: DbSchema.equipments.workingPercentage,
                  monetaryCost: DbSchema.equipments.monetaryCost,
                  inKindCost: DbSchema.equipments.inKindCost,
                  manufactureYear: DbSchema.equipments.manufactureYear,
                  acquisitionDate: DbSchema.equipments.acquisitionDate,
                  installationDate: DbSchema.equipments.installationDate,
                  decommissioningDate: DbSchema.equipments.decommissioningDate,
                  scientificManagerId: DbSchema.equipments.scientificManagerId,
                  manufacturerId: DbSchema.equipments.manufacturerId,
                  supplierId: DbSchema.equipments.supplierId,
                  infrastructureId: DbSchema.equipments.infrastructureId,
                  isFeatured: DbSchema.equipments.isFeatured,
                  institutionId: DbSchema.equipments.institutionId,
                  createdAt: DbSchema.equipments.createdAt,
                  updatedAt: DbSchema.equipments.updatedAt,
                  modifiedBy: DbSchema.equipments.modifiedBy,
                  translations: {
                    locale: DbSchema.equipmentsI18N.locale,
                    name: DbSchema.equipmentsI18N.name,
                    description: DbSchema.equipmentsI18N.description,
                    otherNames: DbSchema.equipmentsI18N.otherNames,
                    acronyms: DbSchema.equipmentsI18N.acronyms,
                  },
                })
                .from(DbSchema.equipments)
                .leftJoin(
                  DbSchema.equipmentsI18N,
                  eq(DbSchema.equipments.id, DbSchema.equipmentsI18N.dataId),
                )
                .where(eq(DbSchema.equipments.id, id)),
            catch: (error) => new Error(`Failed to fetch equipment: ${error}`),
          });

          if (equipment.length === 0) {
            return yield* Effect.fail(new Error('Equipment not found'));
          }

          // Group translations
          const result = equipment.reduce(
            (acc, row) => {
              if (!acc) {
                acc = {
                  ...row,
                  translations: row.translations.locale ? [row.translations] : [],
                };
              } else if (row.translations.locale) {
                acc.translations.push(row.translations);
              }
              return acc;
            },
            null as Schema.Schema.Type<typeof EquipmentSchema> | null,
          );

          return result!;
        });

      const createEquipment = (
        data: Schema.Schema.Type<typeof CreateEquipmentSchema>,
      ) =>
        Effect.gen(function* () {
          const result = yield* Effect.tryPromise({
            try: () =>
              db.transaction(async (tx) => {
                // Insert equipment
                const [equipment] = await tx
                  .insert(DbSchema.equipments)
                  .values({
                    guidId: data.guidId,
                    campusAddressId: data.campusAddressId,
                    isCampusAddressConfidential: data.isCampusAddressConfidential,
                    model: data.model,
                    serialNumber: data.serialNumber,
                    homologationNumber: data.homologationNumber,
                    inventoryNumber: data.inventoryNumber,
                    doi: data.doi,
                    useInClinicalTrial: data.useInClinicalTrial ?? false,
                    isHidden: data.isHidden ?? false,
                    typeId: data.typeId,
                    statusId: data.statusId,
                    workingPercentage: data.workingPercentage,
                    monetaryCost: data.monetaryCost,
                    inKindCost: data.inKindCost,
                    manufactureYear: data.manufactureYear,
                    acquisitionDate: data.acquisitionDate,
                    installationDate: data.installationDate,
                    decommissioningDate: data.decommissioningDate,
                    scientificManagerId: data.scientificManagerId,
                    manufacturerId: data.manufacturerId,
                    supplierId: data.supplierId,
                    infrastructureId: data.infrastructureId,
                    isFeatured: data.isFeatured,
                    institutionId: data.institutionId,
                  })
                  .returning();

                // Insert translations
                if (data.translations.length > 0) {
                  await tx.insert(DbSchema.equipmentsI18N).values(
                    data.translations.map((translation) => ({
                      dataId: equipment.id,
                      locale: translation.locale,
                      name: translation.name,
                      description: translation.description,
                      otherNames: translation.otherNames,
                      acronyms: translation.acronyms,
                    })),
                  );
                }

                return { ...equipment, translations: data.translations };
              }),
            catch: (error) => new Error(`Failed to create equipment: ${error}`),
          });

          return result;
        });

      const updateEquipment = (
        data: { id: string } & Schema.Schema.Type<typeof UpdateEquipmentSchema>,
      ) =>
        Effect.gen(function* () {
          const result = yield* Effect.tryPromise({
            try: () =>
              db.transaction(async (tx) => {
                // Update equipment
                const [equipment] = await tx
                  .update(DbSchema.equipments)
                  .set({
                    guidId: data.guidId,
                    campusAddressId: data.campusAddressId,
                    isCampusAddressConfidential: data.isCampusAddressConfidential,
                    model: data.model,
                    serialNumber: data.serialNumber,
                    homologationNumber: data.homologationNumber,
                    inventoryNumber: data.inventoryNumber,
                    doi: data.doi,
                    useInClinicalTrial: data.useInClinicalTrial,
                    isHidden: data.isHidden,
                    typeId: data.typeId,
                    statusId: data.statusId,
                    workingPercentage: data.workingPercentage,
                    monetaryCost: data.monetaryCost,
                    inKindCost: data.inKindCost,
                    manufactureYear: data.manufactureYear,
                    acquisitionDate: data.acquisitionDate,
                    installationDate: data.installationDate,
                    decommissioningDate: data.decommissioningDate,
                    scientificManagerId: data.scientificManagerId,
                    manufacturerId: data.manufacturerId,
                    supplierId: data.supplierId,
                    infrastructureId: data.infrastructureId,
                    isFeatured: data.isFeatured,
                    institutionId: data.institutionId,
                    updatedAt: new Date().toISOString(),
                  })
                  .where(eq(DbSchema.equipments.id, data.id))
                  .returning();

                if (!equipment) {
                  throw new Error('Equipment not found');
                }

                // Update translations if provided
                if (data.translations) {
                  // Delete existing translations
                  await tx
                    .delete(DbSchema.equipmentsI18N)
                    .where(eq(DbSchema.equipmentsI18N.dataId, data.id));

                  // Insert new translations
                  if (data.translations.length > 0) {
                    await tx.insert(DbSchema.equipmentsI18N).values(
                      data.translations.map((translation) => ({
                        dataId: equipment.id,
                        locale: translation.locale,
                        name: translation.name,
                        description: translation.description,
                        otherNames: translation.otherNames,
                        acronyms: translation.acronyms,
                      })),
                    );
                  }
                }

                return { ...equipment, translations: data.translations || [] };
              }),
            catch: (error) => new Error(`Failed to update equipment: ${error}`),
          });

          return result;
        });

      const deleteEquipment = (id: string) =>
        Effect.gen(function* () {
          yield* Effect.tryPromise({
            try: () =>
              db.transaction(async (tx) => {
                // Delete translations first
                await tx
                  .delete(DbSchema.equipmentsI18N)
                  .where(eq(DbSchema.equipmentsI18N.dataId, id));

                // Delete equipment
                const result = await tx
                  .delete(DbSchema.equipments)
                  .where(eq(DbSchema.equipments.id, id))
                  .returning();

                if (result.length === 0) {
                  throw new Error('Equipment not found');
                }
              }),
            catch: (error) => new Error(`Failed to delete equipment: ${error}`),
          });
        });

      return {
        getAllEquipments,
        getEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } satisfies EquipmentsService;
    }),
).pipe(Layer.provide(DatabaseLive));
