import { ConfigLive } from '@/infrastructure/config/config.live';
import { rooms } from '@rie/db-schema/schemas';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({
        url: env.PG_DATABASE_URL,
        ssl: env.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class RoomsRepositoryLive extends Effect.Service<RoomsRepositoryLive>()(
  'RoomsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all rooms (with their building)
      const findAllRooms = db.makeQuery((exec) =>
        exec((client) =>
          client.query.rooms.findMany({
            columns: {
              id: true,
              number: true,
              area: true,
              floorLoad: true,
              buildingId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              building: {
                columns: { id: true },
              },
            },
          }),
        ),
      );

      // — Fetch one room by ID
      const findRoomById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.rooms.findFirst({
            where: eq(rooms.id, id),
            columns: {
              id: true,
              number: true,
              area: true,
              floorLoad: true,
              buildingId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              building: {
                columns: { id: true },
              },
            },
          }),
        ),
      );

      // — Create a room
      const createRoom = db.makeQuery(
        (
          exec,
          params: {
            number: string;
            area?: number | null;
            floorLoad?: number | null;
            buildingId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(rooms)
              .values({
                number: params.number,
                area: params.area,
                floorLoad: params.floorLoad,
                buildingId: params.buildingId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: rooms.id,
                number: rooms.number,
                area: rooms.area,
                floorLoad: rooms.floorLoad,
                buildingId: rooms.buildingId,
                createdAt: rooms.createdAt,
                updatedAt: rooms.updatedAt,
                modifiedBy: rooms.modifiedBy,
              }),
          ),
      );

      // — Update a room
      const updateRoom = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            number?: string;
            area?: number | null;
            floorLoad?: number | null;
            buildingId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(rooms)
              .set({
                number: params.number,
                area: params.area,
                floorLoad: params.floorLoad,
                buildingId: params.buildingId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(rooms.id, params.id))
              .returning({
                id: rooms.id,
                number: rooms.number,
                area: rooms.area,
                floorLoad: rooms.floorLoad,
                buildingId: rooms.buildingId,
                createdAt: rooms.createdAt,
                updatedAt: rooms.updatedAt,
                modifiedBy: rooms.modifiedBy,
              }),
          ),
      );

      // — Delete a room
      const deleteRoom = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(rooms)
            .where(eq(rooms.id, id))
            .returning({ id: rooms.id }),
        ),
      );

      return {
        findAllRooms,
        findRoomById,
        createRoom,
        updateRoom,
        deleteRoom,
      } as const;
    }),
  },
) {}
