import { handleEffectError } from '@/api/v2/utils/error-handler';
import { PeopleRuntime } from '@/infrastructure/runtimes/people.runtime';
import { PeopleServiceLive } from '@/infrastructure/services/people.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreatePersonSchema,
    PersonResponseSchema,
    ResourceIdSchema,
    UpdatePersonSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

export const createPersonRoute = describeRoute({
    description: 'Create a Person',
    operationId: 'createPerson',
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(CreatePersonSchema) },
        },
    },
    responses: {
        201: {
            description: 'Person created',
            content: { 'application/json': { schema: resolver(PersonResponseSchema) } },
        },
        400: { /* validation error */ },
        404: { /* FK not found */ },
        500: { /* generic */ },
    },
    tags: ['People'],
});

export const updatePersonRoute = describeRoute({
    description: 'Update a Person',
    operationId: 'updatePerson',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(UpdatePersonSchema) },
        },
    },
    responses: {
        200: {
            description: 'Person updated',
            content: { 'application/json': { schema: resolver(PersonResponseSchema) } },
        },
        400: {},
        404: {},
        500: {},
    },
    tags: ['People'],
});

export const deletePersonRoute = describeRoute({
    description: 'Delete a Person',
    operationId: 'deletePerson',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Person deleted',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
                    ),
                },
            },
        },
        404: {},
        500: {},
    },
    tags: ['People'],
});

export const getPersonByIdRoute = describeRoute({
    description: 'Get a Person by ID',
    operationId: 'getPersonById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Person found',
            content: { 'application/json': { schema: resolver(PersonResponseSchema) } },
        },
        404: {
            description: 'Person not found',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ error: Schema.String }),
                    ),
                },
            },
        },
        500: {
            description: 'Internal server error',
            content: {
                'application/json': {
                    schema: resolver(
                        Schema.Struct({ error: Schema.String }),
                    ),
                },
            },
        },
    },
    tags: ['People'],
});

export const getAllPeopleRoute = describeRoute({
    description: 'List all People',
    operationId: 'getAllPeople',
    responses: {
        200: {
            description: 'People returned',
            content: {
                'application/json': { schema: resolver(Schema.Array(PersonResponseSchema)) },
            },
        },
        500: {},
    },
    tags: ['People'],
});

const peopleRoute = new Hono();

peopleRoute.get(
    '/',
    getAllPeopleRoute,
    async (ctx) => {
        const program = Effect.gen(function* () {
            const svc = yield* PeopleServiceLive;
            return yield* svc.getAllPeople();
        });
        const result = await PeopleRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

peopleRoute.get(
    '/:id',
    getPersonByIdRoute,
    async (ctx) => {
        const id = ctx.req.param('id');
        const program = Effect.gen(function* () {
            const svc = yield* PeopleServiceLive;
            return yield* svc.getPersonById(id);
        });
        const result = await PeopleRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

peopleRoute.post(
    '/',
    createPersonRoute,
    effectValidator('json', CreatePersonSchema),
    async (ctx) => {
        const body = ctx.req.valid('json');
        const program = Effect.gen(function* () {
            const svc = yield* PeopleServiceLive;
            return yield* svc.createPerson(body);
        });
        const result = await PeopleRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value, 201);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

peopleRoute.put(
    '/:id',
    updatePersonRoute,
    effectValidator('json', UpdatePersonSchema),
    async (ctx) => {
        const id = ctx.req.param('id');
        const body = ctx.req.valid('json');
        const program = Effect.gen(function* () {
            const svc = yield* PeopleServiceLive;
            return yield* svc.updatePerson({ id, ...body });
        });
        const result = await PeopleRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json(result.value);
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

peopleRoute.delete(
    '/:id',
    deletePersonRoute,
    async (ctx) => {
        const id = ctx.req.param('id');
        const program = Effect.gen(function* () {
            const svc = yield* PeopleServiceLive;
            return yield* svc.deletePerson(id);
        });
        const result = await PeopleRuntime.runPromiseExit(program);
        const errorResponse = handleEffectError(ctx, result);
        if (errorResponse) {
            return errorResponse;
        }

        if (Exit.isSuccess(result)) {
            return ctx.json({ success: true, message: 'Person deleted' });
        }

        return ctx.json({ error: 'An error occurred' }, 500);
    },
);

export { peopleRoute };
