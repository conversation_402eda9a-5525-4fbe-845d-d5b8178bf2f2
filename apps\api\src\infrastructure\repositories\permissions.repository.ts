import { ConfigLive } from '@/infrastructure/config/config.live';
import type {
  DbPermissionAction,
  DbPermissionDomain,
} from '@rie/db-schema/entity-types';
import { permissions } from '@rie/db-schema/schemas';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { and, eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class PermissionsRepositoryLive extends Effect.Service<PermissionsRepositoryLive>()(
  'PermissionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllPermissions = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.permissions.findMany({
            columns: {
              id: true,
              domain: true,
              action: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      const findPermissionById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.permissions.findFirst({
            where: eq(permissions.id, id),
            columns: {
              id: true,
              domain: true,
              action: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      const checkPermissionExists = dbClient.makeQuery(
        (
          execute,
          data: { domain: DbPermissionDomain; action: DbPermissionAction },
        ) => {
          return execute((client) =>
            client.query.permissions.findFirst({
              where: and(
                eq(permissions.domain, data.domain),
                eq(permissions.action, data.action),
              ),
              columns: {
                id: true,
              },
            }),
          );
        },
      );

      const createPermission = dbClient.makeQuery(
        (
          execute,
          params: {
            domain: DbPermissionDomain;
            action: DbPermissionAction;
          },
        ) => {
          return execute((client) => {
            return client.insert(permissions).values(params).returning({
              id: permissions.id,
              domain: permissions.domain,
              action: permissions.action,
              createdAt: permissions.createdAt,
              updatedAt: permissions.updatedAt,
            });
          });
        },
      );

      const updatePermission = dbClient.makeQuery(
        (
          execute,
          data: {
            id: string;
            domain: DbPermissionDomain;
            action: DbPermissionAction;
          },
        ) => {
          return execute((client) =>
            client
              .update(permissions)
              .set({
                domain: data.domain,
                action: data.action,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(permissions.id, data.id))
              .returning({
                id: permissions.id,
                domain: permissions.domain,
                action: permissions.action,
                createdAt: permissions.createdAt,
                updatedAt: permissions.updatedAt,
              }),
          );
        },
      );

      const deletePermission = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(permissions)
            .where(eq(permissions.id, id))
            .returning({ id: permissions.id }),
        );
      });

      const findAllRoles = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.roles.findMany({
            columns: {
              id: true,
              name: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      const findAllPermissionGroups = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.permissionGroups.findMany({
            columns: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      return {
        findAllPermissions,
        findPermissionById,
        checkPermissionExists,
        createPermission,
        updatePermission,
        deletePermission,
        findAllPermissionGroups,
        findAllRoles,
      } as const;
    }),
  },
) {}
