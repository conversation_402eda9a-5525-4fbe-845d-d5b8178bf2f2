import { equipmentAssociatedExcellenceHubs, locales, users } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const excellenceHubs = pgTable('excellence_hubs', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const excellenceHubsRelations = relations(
  excellenceHubs,
  ({ many }) => ({
    translations: many(excellenceHubsI18N),
    associatedEquipments: many(equipmentAssociatedExcellenceHubs),
  }),
);

export const excellenceHubsI18N = pgTable(
  'excellence_hubs_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => excellenceHubs.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const excellenceHubsI18NRelations = relations(
  excellenceHubsI18N,
  ({ one }) => ({
    excellenceHubs: one(excellenceHubs, {
      fields: [excellenceHubsI18N.dataId],
      references: [excellenceHubs.id],
    }),
    locale: one(locales, {
      fields: [excellenceHubsI18N.locale],
      references: [locales.code],
    }),
  }),
);
