import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '@/migration-scripts/constants';
import { EquipmentCategoryParentsMigrationConverter } from './converters/07-03-convert-equipment-category-parents-inserts';

async function convertEquipmentCategoryParentsData() {
  const scriptDir = __dirname;
  // Input file is in the data directory
  const inputFile = path.join(
    scriptDir,
    'data',
    'mysql_riedb-content_24-04-2025.sql',
  );
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  // Check if input file exists
  try {
    await fs.access(inputFile);
  } catch (error) {
    console.error(`Input file not found: ${inputFile}`);
    console.error(
      'Please make sure the MySQL dump file is in the correct location.',
    );
    process.exit(1);
  }

  // Create converter and run conversion
  const converter = new EquipmentCategoryParentsMigrationConverter();
  await converter.convertFile(inputFile, outputFile);
}

// Run the conversion
convertEquipmentCategoryParentsData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
