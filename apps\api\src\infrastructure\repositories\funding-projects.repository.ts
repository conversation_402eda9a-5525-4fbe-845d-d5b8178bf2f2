import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({
        url: env.PG_DATABASE_URL,
        ssl: env.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class FundingProjectsRepositoryLive extends Effect.Service<FundingProjectsRepositoryLive>()(
  'FundingProjectsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all projects
      const findAllFundingProjects = db.makeQuery((exec) =>
        exec((client) =>
          client.query.fundingProjects.findMany({
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one
      const findFundingProjectById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.fundingProjects.findFirst({
            where: eq(DBSchema.fundingProjects.id, id),
            columns: {
              id: true,
              holderId: true,
              typeId: true,
              fciId: true,
              synchroId: true,
              obtainingYear: true,
              endDate: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                },
              },
            },
          }),
        ),
      );

      // — Create
      const createFundingProject = db.makeQuery(
        (
          exec,
          params: {
            holderId: string;
            typeId: string;
            fciId?: string | null;
            synchroId: string;
            obtainingYear: number;
            endDate?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.fundingProjects)
              .values({
                holderId: params.holderId,
                typeId: params.typeId,
                fciId: params.fciId,
                synchroId: params.synchroId,
                obtainingYear: params.obtainingYear,
                endDate: params.endDate,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.fundingProjects.id,
                holderId: DBSchema.fundingProjects.holderId,
                typeId: DBSchema.fundingProjects.typeId,
                fciId: DBSchema.fundingProjects.fciId,
                synchroId: DBSchema.fundingProjects.synchroId,
                obtainingYear: DBSchema.fundingProjects.obtainingYear,
                endDate: DBSchema.fundingProjects.endDate,
                createdAt: DBSchema.fundingProjects.createdAt,
                updatedAt: DBSchema.fundingProjects.updatedAt,
                modifiedBy: DBSchema.fundingProjects.modifiedBy,
              }),
          ),
      );

      // — Update
      const updateFundingProject = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            holderId?: string;
            typeId?: string;
            fciId?: string | null;
            synchroId?: string;
            obtainingYear?: number;
            endDate?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.fundingProjects)
              .set({
                holderId: params.holderId,
                typeId: params.typeId,
                fciId: params.fciId,
                synchroId: params.synchroId,
                obtainingYear: params.obtainingYear,
                endDate: params.endDate,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.fundingProjects.id, params.id))
              .returning({
                id: DBSchema.fundingProjects.id,
                holderId: DBSchema.fundingProjects.holderId,
                typeId: DBSchema.fundingProjects.typeId,
                fciId: DBSchema.fundingProjects.fciId,
                synchroId: DBSchema.fundingProjects.synchroId,
                obtainingYear: DBSchema.fundingProjects.obtainingYear,
                endDate: DBSchema.fundingProjects.endDate,
                createdAt: DBSchema.fundingProjects.createdAt,
                updatedAt: DBSchema.fundingProjects.updatedAt,
                modifiedBy: DBSchema.fundingProjects.modifiedBy,
              }),
          ),
      );

      // — Delete
      const deleteFundingProject = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.fundingProjects)
            .where(eq(DBSchema.fundingProjects.id, id))
            .returning({ id: DBSchema.fundingProjects.id }),
        ),
      );

      // — Create + translations
      const createFundingProjectWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            project: {
              holderId: string;
              typeId: string;
              fciId?: string | null;
              synchroId: string;
              obtainingYear: number;
              endDate?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name: string;
              description?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            const [proj] = await client
              .insert(DBSchema.fundingProjects)
              .values({
                holderId: params.project.holderId,
                typeId: params.project.typeId,
                fciId: params.project.fciId,
                synchroId: params.project.synchroId,
                obtainingYear: params.project.obtainingYear,
                endDate: params.project.endDate,
                modifiedBy: params.project.modifiedBy,
              })
              .returning({ id: DBSchema.fundingProjects.id });

            if (params.translations.length > 0) {
              await client.insert(DBSchema.fundingProjectsI18N).values(
                params.translations.map((t) => ({
                  dataId: proj.id,
                  locale: t.locale,
                  name: t.name,
                  description: t.description,
                })),
              );
            }

            return proj;
          }),
      );

      // — Update translations
      const updateFundingProjectTranslations = db.makeQuery(
        (
          exec,
          params: {
            projectId: string;
            translations: Array<{
              locale: string;
              name: string;
              description?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            await client
              .delete(DBSchema.fundingProjectsI18N)
              .where(
                eq(DBSchema.fundingProjectsI18N.dataId, params.projectId),
              );

            if (params.translations.length > 0) {
              return await client
                .insert(DBSchema.fundingProjectsI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.projectId,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                  })),
                )
                .returning({
                  id: DBSchema.fundingProjectsI18N.id,
                  dataId: DBSchema.fundingProjectsI18N.dataId,
                  locale: DBSchema.fundingProjectsI18N.locale,
                  name: DBSchema.fundingProjectsI18N.name,
                  description: DBSchema.fundingProjectsI18N.description,
                });
            }

            return [];
          }),
      );

      return {
        findAllFundingProjects,
        findFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
        createFundingProjectWithTranslations,
        updateFundingProjectTranslations,
      } as const;
    }),
  },
) { }
