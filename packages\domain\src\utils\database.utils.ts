import type { NonEmptyArray } from '@/types/common.types';

/**
 * Type guard to check if a translation array has at least one non-null value for a given field
 */
export const hasValidTranslation = <
  T extends { locale: string } & Record<string, string | null>,
>(
  translations: ReadonlyArray<T>,
  fieldName: keyof Omit<T, 'locale'>,
): translations is NonEmptyArray<T> => {
  return translations.some((t) => t[fieldName] !== null && t[fieldName] !== '');
};

export const getTranslatedFieldFromTranslations = <
  TTranslation extends { locale: string } & Record<string, string | null>,
>(
  translations: ReadonlyArray<TTranslation>,
  fieldName: keyof Omit<TTranslation, 'locale'>,
  requestedLocale: string,
  fallbackLocale: string,
): string => {
  const primaryTranslation = translations.find(
    (t) => t.locale === requestedLocale,
  );
  if (primaryTranslation?.[fieldName]) {
    return primaryTranslation[fieldName];
  }

  const fallbackTranslation = translations.find(
    (t) => t.locale === fallbackLocale,
  );
  if (fallbackTranslation?.[fieldName]) {
    return fallbackTranslation[fieldName];
  }

  throw new Error(`No translation found for field ${String(fieldName)}`);
};
