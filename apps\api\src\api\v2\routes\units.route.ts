import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UnitsRuntime } from '@/infrastructure/runtimes/units.runtime';
import { UnitsServiceLive } from '@/infrastructure/services/units.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateUnitSchema,
    UpdateUnitSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

const unitsRoute = new Hono();

unitsRoute.get('/', async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* UnitsServiceLive;
        return yield* svc.getAllUnits();
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.get('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* UnitsServiceLive;
        return yield* svc.getUnitById(id);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.post('/', effectValidator('json', CreateUnitSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* UnitsServiceLive;
        return yield* svc.createUnit(body);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.put('/:id', effectValidator('json', UpdateUnitSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* UnitsServiceLive;
        return yield* svc.updateUnit({ id, ...body });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.delete('/:id', async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* UnitsServiceLive;
        return yield* svc.deleteUnit(id);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Unit deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { unitsRoute };
