import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UnitsRuntime } from '@/infrastructure/runtimes/units.runtime';
import { UnitsServiceLive } from '@/infrastructure/services/units.service';
import { effectValidator } from '@hono/effect-validator';
import { CreateUnitSchema, UpdateUnitSchema } from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Import additional schemas
import { ResourceIdSchema, UnitSchema } from '@rie/domain/schemas';

// OpenAPI route descriptions
export const getAllUnitsRoute = describeRoute({
  description: 'Get all Units',
  operationId: 'getAllUnits',
  responses: {
    200: {
      description: 'List of units',
      content: {
        'application/json': { schema: resolver(Schema.Array(UnitSchema)) },
      },
    },
    500: { description: 'Internal server error' },
  },
  tags: ['Units'],
});

export const getUnitByIdRoute = describeRoute({
  description: 'Get a Unit by ID',
  operationId: 'getUnitById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  responses: {
    200: {
      description: 'Unit found',
      content: { 'application/json': { schema: resolver(UnitSchema) } },
    },
    404: {
      description: 'Unit not found',
      content: {
        'application/json': {
          schema: resolver(Schema.Struct({ error: Schema.String })),
        },
      },
    },
    500: { description: 'Internal server error' },
  },
  tags: ['Units'],
});

export const createUnitRoute = describeRoute({
  description: 'Create a Unit',
  operationId: 'createUnit',
  requestBody: {
    required: true,
    content: {
      'application/json': { schema: resolver(CreateUnitSchema) },
    },
  },
  responses: {
    201: {
      description: 'Unit created',
      content: { 'application/json': { schema: resolver(UnitSchema) } },
    },
    400: { description: 'Validation error' },
    500: { description: 'Internal server error' },
  },
  tags: ['Units'],
});

export const updateUnitRoute = describeRoute({
  description: 'Update a Unit',
  operationId: 'updateUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': { schema: resolver(UpdateUnitSchema) },
    },
  },
  responses: {
    200: {
      description: 'Unit updated',
      content: { 'application/json': { schema: resolver(UnitSchema) } },
    },
    400: { description: 'Validation error' },
    404: { description: 'Unit not found' },
    500: { description: 'Internal server error' },
  },
  tags: ['Units'],
});

export const deleteUnitRoute = describeRoute({
  description: 'Delete a Unit',
  operationId: 'deleteUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
    },
  ],
  responses: {
    200: {
      description: 'Unit deleted',
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
    },
    404: { description: 'Unit not found' },
    500: { description: 'Internal server error' },
  },
  tags: ['Units'],
});

const unitsRoute = new Hono();

unitsRoute.get('/', getAllUnitsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const svc = yield* UnitsServiceLive;
    return yield* svc.getAllUnits();
  });
  const result = await UnitsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.get('/:id', getUnitByIdRoute, async (ctx) => {
  const id = ctx.req.param('id');
  const program = Effect.gen(function* () {
    const svc = yield* UnitsServiceLive;
    return yield* svc.getUnitById(id);
  });
  const result = await UnitsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.post(
  '/',
  createUnitRoute,
  effectValidator('json', CreateUnitSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const svc = yield* UnitsServiceLive;
      return yield* svc.createUnit(body);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.put(
  '/:id',
  updateUnitRoute,
  effectValidator('json', UpdateUnitSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const svc = yield* UnitsServiceLive;
      return yield* svc.updateUnit({ id, ...body });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.delete('/:id', deleteUnitRoute, async (ctx) => {
  const id = ctx.req.param('id');
  const program = Effect.gen(function* () {
    const svc = yield* UnitsServiceLive;
    return yield* svc.deleteUnit(id);
  });
  const result = await UnitsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json({ success: true, message: 'Unit deleted' });
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

export { unitsRoute };
