import {
  equipmentAssociatedApplicationSectors,
  locales,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const applicationSectors = pgTable('application_sectors', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const applicationSectorsI18N = pgTable(
  'application_sectors_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => applicationSectors.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const applicationSectorsRelations = relations(
  applicationSectors,
  ({ many }) => ({
    translations: many(applicationSectorsI18N),
    equipmentApplicationSectors: many(equipmentAssociatedApplicationSectors),
  }),
);

export const applicationSectorsI18NRelations = relations(
  applicationSectorsI18N,
  ({ one }) => ({
    applicationSector: one(applicationSectors, {
      fields: [applicationSectorsI18N.dataId],
      references: [applicationSectors.id],
    }),
    locales: one(locales, {
      fields: [applicationSectorsI18N.locale],
      references: [locales.code],
    }),
  }),
);
