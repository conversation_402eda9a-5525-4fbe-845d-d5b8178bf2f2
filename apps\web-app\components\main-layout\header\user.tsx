'use client';

import { But<PERSON> } from '@/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/ui/dropdown-menu';
import { useTranslations } from 'next-intl';
import { HiUserCircle } from 'react-icons/hi';
import { signOut } from '@/lib/better-auth';

type UserProps = {
  email: string;
  fullName: string;
};
export const User = ({ email, fullName }: UserProps) => {
  const tUser = useTranslations('user');

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="rounded-full" size="icon" variant="secondary">
          <HiUserCircle size={28} />
          <span className="sr-only">{fullName}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{tUser('myAccount')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>{fullName}</DropdownMenuItem>
        <DropdownMenuItem>{email}</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="hover:cursor-pointer"
          onClick={() => signOut({})}
        >
          {tUser('logout')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
