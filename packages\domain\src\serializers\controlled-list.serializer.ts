import {
  type ControlledList,
  type ControlledListItem,
  ControlledListItemSchema,
  DbControlledListSchema,
} from '@/schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const DbControlledListToControlledListItem = Schema.transformOrFail(
  DbControlledListSchema,
  ControlledListItemSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse controlled list',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const transformControlledListData = (
  data: ControlledList[],
  requestedLocale: string | null,
  fallbackLocale: string,
): ControlledListItem[] => {
  // Group by ID
  const itemsById = data.reduce(
    (acc, item) => {
      if (!acc[item.id]) {
        acc[item.id] = [item];
      } else {
        acc[item.id]?.push(item);
      }
      return acc;
    },
    {} as Record<string, typeof data>,
  );

  // For each ID, select the right locale
  return Object.entries(itemsById).map(([id, items]) => {
    // Try to find the requested locale
    const requestedItem = items.find((item) => item.locale === requestedLocale);

    // If not found, try to find the fallback locale
    const fallbackItem = !requestedItem
      ? items.find((item) => item.locale === fallbackLocale)
      : null;

    // Use requested locale if available, then fallback locale, then any item
    const selectedItem = requestedItem || fallbackItem || items[0];

    return {
      value: id,
      label: selectedItem?.name || id,
    };
  });
};
