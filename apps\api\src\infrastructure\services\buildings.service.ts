import { BuildingsRepositoryLive } from '@/infrastructure/repositories/buildings.repository';
import { BuildingNotFoundError } from '@rie/domain/errors';
import type {
  CreateBuildingPayload,
  UpdateBuildingPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class BuildingsServiceLive extends Effect.Service<BuildingsServiceLive>()(
  'BuildingsServiceLive',
  {
    dependencies: [BuildingsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllBuildings = () => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const all = yield* repo.findAllBuildings();
          const t1 = performance.now();
          console.log(`Call to getAllBuildings took ${t1 - t0} ms.`);
          return all;
        });
      };

      const getBuildingById = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const building = yield* repo.findBuildingById(id);
          if (!building) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const t1 = performance.now();
          console.log(`Call to getBuildingById took ${t1 - t0} ms.`);
          return building;
        });
      };
      type CreateParams = CreateBuildingPayload;
      const createBuilding = (data: CreateParams) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          return yield* repo
            .createBuildingWithTranslations({
              building: data,
              translations: data.translations ? [...data.translations] : [],
            })
            .pipe(
              Effect.catchTag('DatabaseError', (err) => {
                return Effect.fail(new Error(`Database error: ${err.message}`));
              }),
            );
        });

      type UpdateParams = UpdateBuildingPayload;
      const updateBuilding = ({ id, ...rest }: UpdateParams) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existing = yield* repo.findBuildingById(id);
          if (!existing) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const [updated] = yield* repo.updateBuildingWithTranslations({
            buildingId: id,
            building: rest,
            translations: rest.translations ? [...rest.translations] : [],
          });
          const t1 = performance.now();
          console.log(`Call to updateBuilding took ${t1 - t0} ms.`);
          return updated;
        });
      };

      const deleteBuilding = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existing = yield* repo.findBuildingById(id);
          if (!existing) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const result = yield* repo.deleteBuilding(id);
          const t1 = performance.now();
          console.log(`Call to deleteBuilding took ${t1 - t0} ms.`);
          return result.length > 0;
        });
      };

      return {
        getAllBuildings,
        getBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) {}
