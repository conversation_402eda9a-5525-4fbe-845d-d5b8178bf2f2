import { getAllPermissionsGroups } from '@/services/permissions/permissions-groups.service';
import { queryOptions } from '@tanstack/react-query';

export const getAllPermissionsGroupsOptions = () => {
  return queryOptions({
    queryFn: getAllPermissionsGroups,
    queryKey: ['permissionGroups'],
    select: (data) =>
      data.map((permissionGroup) => ({
        label: permissionGroup.name,
        value: permissionGroup.id,
      })),
  });
};
