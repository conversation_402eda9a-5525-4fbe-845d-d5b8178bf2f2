import createJiti from 'jiti';
import createNextIntlPlugin from 'next-intl/plugin';
import { fileURLToPath } from 'node:url';

const jiti = createJiti(fileURLToPath(import.meta.url));

jiti('./env');

const withNextIntl = createNextIntlPlugin('./i18n/i18n.ts');

// Read NEXT_ENABLE_DEBUG from environment, default to false if not set
const isDebugEnabled = process.env.NEXT_ENABLE_DEBUG === 'true';

/** @type {import('next').NextConfig} */
const nextConfig = {
  rewrites: async () => [
    {
      source: '/ingest/static/:path*',
      destination: 'https://us-assets.i.posthog.com/static/:path*',
    },
    {
      source: '/ingest/:path*',
      destination: 'https://us.i.posthog.com/:path*',
    },
    {
      source: '/ingest/decide',
      destination: 'https://us.i.posthog.com/decide',
    },
  ],
  skipTrailingSlashRedirect: true,
  basePath: process.env.NEXT_PUBLIC_BASE_PATH,
  output: 'standalone',
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds:
      process.env.NODE_ENV === 'production' && !isDebugEnabled,
  },
  trailingSlash: true,
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production' && !isDebugEnabled,
  },
  webpack: (config, { dev, webpack }) => {
    // Known issue: https://github.com/vercel/next.js/discussions/50177
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^pg-native$|^cloudflare:sockets$/,
      }),
    );
    // Enable source maps and disable minification in production when debug is enabled
    if (!dev && isDebugEnabled) {
      // Disable minimization for both client and server
      config.optimization.minimize = false;
      config.optimization.minimizer = [];
      // Enable source maps
      config.devtool = 'source-map';
    }

    return config;
  },
  experimental: {
    // Enable server source maps in production when debug is enabled
    serverSourceMaps: isDebugEnabled,
  },
  productionBrowserSourceMaps: isDebugEnabled,
};

export default withNextIntl(nextConfig);
