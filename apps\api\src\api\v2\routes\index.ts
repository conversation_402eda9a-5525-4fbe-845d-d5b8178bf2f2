import authRouter from '@/api/v2/routes/auth.route';
// Directory entity routes
import { buildingsRoute } from '@/api/v2/routes/buildings.route';
import { campusesRoute } from '@/api/v2/routes/campuses.route';
import { controlledListsRoute } from '@/api/v2/routes/controlled-lists.route';
import { institutionsRoute } from '@/api/v2/routes/establishments.route';
import { fundingProjectsRoute } from '@/api/v2/routes/funding-projects.route';
import { peopleRoute } from '@/api/v2/routes/people.route';
import permissionsGroupsRoute from '@/api/v2/routes/permissions-groups.route';
import permissionsRoute from '@/api/v2/routes/permissions.route';
import { rolesRoute } from '@/api/v2/routes/roles.route';
import { roomsRoute } from '@/api/v2/routes/rooms.route';
import serviceOfferRouter from '@/api/v2/routes/service-offer.route';
import { unitsRoute } from '@/api/v2/routes/units.route';
import { vendorsRoute } from '@/api/v2/routes/vendors.route';
import { ConfigLive } from '@/infrastructure/config/config.live';
import { swaggerUI } from '@hono/swagger-ui';
import { Effect } from 'effect';
import { Hono } from 'hono';
import { openAPISpecs } from 'hono-openapi';

const apiRouter = new Hono();

const config = Effect.runSync(
  Effect.provide(
    Effect.flatMap(ConfigLive, (config) => Effect.succeed(config)),
    ConfigLive.Default,
  ),
);
apiRouter.get(
  '/openapi',
  openAPISpecs(apiRouter, {
    documentation: {
      info: {
        title: 'RIE Service Offers API',
        version: '1.0.0',
        description: 'API for getting Service Offers',
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      security: [
        {
          bearerAuth: [],
        },
      ],
      servers: [
        {
          url: config.NEXT_PUBLIC_API_BASE_URL,
          description: 'Local server',
        },
      ],
    },
  }),
);

apiRouter.get('/doc', swaggerUI({ url: '/api/openapi' }));

apiRouter.route('/v2/auth', authRouter);
apiRouter.route('/v2/controlled-lists', controlledListsRoute);
apiRouter.route('/v2/permission-groups', permissionsGroupsRoute);
apiRouter.route('/v2/permissions', permissionsRoute);
apiRouter.route('/v2/roles', rolesRoute);
apiRouter.route('/v2/service-offers', serviceOfferRouter);

// Directory entity routes with French path names maintained for backwards compatibility,
// but using English variables for code clarity
apiRouter.route('/v2/buildings', buildingsRoute);
apiRouter.route('/v2/vendors', vendorsRoute);
apiRouter.route('/v2/campuses', campusesRoute);
apiRouter.route('/v2/institutions', institutionsRoute);
apiRouter.route('/v2/rooms', roomsRoute);
apiRouter.route('/v2/people', peopleRoute);
apiRouter.route('/v2/funding-projects', fundingProjectsRoute);
apiRouter.route('/v2/units', unitsRoute);
export default apiRouter;
