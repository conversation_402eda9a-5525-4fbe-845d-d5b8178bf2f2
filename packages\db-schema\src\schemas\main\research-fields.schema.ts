import {
  equipmentAssociatedResearchFields,
  equipments,
  locales,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const researchFields = pgTable('research_fields', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const researchFieldsRelations = relations(
  researchFields,
  ({ many }) => ({
    translations: many(researchFieldsI18N),
    equipmentResearchFields: many(equipmentAssociatedResearchFields),
  }),
);

export const researchFieldsI18N = pgTable(
  'research_fields_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => researchFields.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const researchFieldsI18NRelations = relations(
  researchFieldsI18N,
  ({ one }) => ({
    researchFields: one(researchFields, {
      fields: [researchFieldsI18N.dataId],
      references: [researchFields.id],
    }),
    locale: one(locales, {
      fields: [researchFieldsI18N.locale],
      references: [locales.code],
    }),
  }),
);

export const equipmentAssociatedResearchFieldRelations = relations(
  equipmentAssociatedResearchFields,
  ({ one }) => ({
    equipment: one(equipments, {
      fields: [equipmentAssociatedResearchFields.equipmentId],
      references: [equipments.id],
    }),
    researchFields: one(researchFields, {
      fields: [equipmentAssociatedResearchFields.researchFieldId],
      references: [researchFields.id],
    }),
  }),
);
