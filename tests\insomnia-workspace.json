{"_type": "export", "__export_format": 4, "__export_date": "2025-05-28T12:00:00.000Z", "__export_source": "insomnia.desktop.app:v11.1.0", "resources": [{"_id": "req_0000000000000001", "parentId": "fld_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/auth/sign-in/email", "name": "Sign In Email", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ _.email }}\",\n  \"password\": \"{{ _.password }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_0000000000000001"}], "authentication": {}, "metaSortKey": -1716811200000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000001", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Auth & API Requests", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716811200000, "_type": "request_group"}, {"_id": "wrk_0000000000000001", "parentId": null, "modified": 1716811200000, "created": 1716811200000, "name": "Localhost API Collection", "description": "API collection for localhost development", "scope": "collection", "_type": "workspace"}, {"_id": "req_0000000000000002", "parentId": "fld_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/auth/get-session", "name": "Get Session", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716811100000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0000000000000003", "parentId": "fld_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/roles", "name": "Get Roles", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716811000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000002", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Buildings", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716810000000, "_type": "request_group"}, {"_id": "req_buildings_get", "parentId": "fld_0000000000000002", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/buildings", "name": "Get Buildings", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716810000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store buildings list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('buildings_list', response);\n    insomnia.environment.set('building_id', response[0].id);\n    console.log('Buildings list stored:', response.length, 'items');\n    console.log('First building ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No buildings found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_buildings_get_by_id", "parentId": "fld_0000000000000002", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/buildings/{{ _.building_id }}", "name": "Get Building by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716810500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Building retrieved successfully', () => {\n  const response = insomnia.response.json();\n  if (response && response.id) {\n    console.log('Building retrieved successfully, ID:', response.id);\n    insomnia.expect(response.id).toBeTruthy();\n    insomnia.expect(response).toHaveProperty('id');\n  } else {\n    console.log('Failed to retrieve building or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_buildings_post", "parentId": "fld_0000000000000002", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/buildings", "name": "Create Building", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"campusId\": \"{{ _.campus_id }}\",\n  \"civicAddressId\": null,\n  \"sadId\": \"NEW_BUILDING_001\",\n  \"diId\": null,\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"New Science Building\",\n      \"description\": \"New main science building\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Nouveau Bâtiment des Sciences\",\n      \"description\": \"Nouveau bâtiment principal des sciences\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_buildings_post"}], "authentication": {}, "metaSortKey": -1716809900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_buildings_put", "parentId": "fld_0000000000000002", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/buildings/{{ _.building_id }}", "name": "Update Building", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"campusId\": \"{{ _.campus_id }}\",\n  \"civicAddressId\": null,\n  \"sadId\": \"UPDATED_BUILDING_001\",\n  \"diId\": null,\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Updated Science Building\",\n      \"description\": \"Updated main science building\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Bâtiment des Sciences Mis à Jour\",\n      \"description\": \"Bâtiment principal des sciences mis à jour\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_buildings_put"}], "authentication": {}, "metaSortKey": -1716809800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_buildings_delete", "parentId": "fld_0000000000000002", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/buildings/{{ _.building_id }}", "name": "Delete Building", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716809700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000003", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Campuses", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716809000000, "_type": "request_group"}, {"_id": "req_campuses_get", "parentId": "fld_0000000000000003", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/campuses", "name": "Get Campuses", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716809000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store campuses list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('campuses_list', response);\n    insomnia.environment.set('campus_id', response[0].id);\n    console.log('Campuses list stored:', response.length, 'items');\n    console.log('First campus ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No campuses found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_campuses_get_by_id", "parentId": "fld_0000000000000003", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/campuses/{{ _.campus_id }}", "name": "Get Campus by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716808500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_campuses_post", "parentId": "fld_0000000000000003", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/campuses", "name": "Create Campus", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"sadId\": \"NEW_CAMPUS_001\",\n  \"institutionId\": \"{{ _.institution_id }}\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"New Main Campus\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Nouveau Campus Principal\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_campuses_post"}], "authentication": {}, "metaSortKey": -1716808900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_campuses_put", "parentId": "fld_0000000000000003", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/campuses/{{ _.campus_id }}", "name": "Update Campus", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"sadId\": \"UPDATED_CAMPUS_001\",\n  \"institutionId\": \"{{ _.institution_id }}\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Updated Main Campus\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Campus Principal Mis à Jour\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_campuses_put"}], "authentication": {}, "metaSortKey": -1716808800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_campuses_delete", "parentId": "fld_0000000000000003", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/campuses/{{ _.campus_id }}", "name": "Delete Campus", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716808700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000004", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Funding Projects", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716808000000, "_type": "request_group"}, {"_id": "req_funding_projects_get", "parentId": "fld_0000000000000004", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/funding-projects", "name": "Get Funding Projects", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716808000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store funding projects list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('funding_projects_list', response);\n    insomnia.environment.set('funding_project_id', response[0].id);\n    console.log('Funding projects list stored:', response.length, 'items');\n    console.log('First funding project ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No funding projects found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_funding_projects_get_by_id", "parentId": "fld_0000000000000004", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/funding-projects/{{ _.funding_project_id }}", "name": "Get Funding Project by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716807500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_funding_projects_post", "parentId": "fld_0000000000000004", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/funding-projects", "name": "Create Funding Project", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"holderId\": \"{{ _.person_id }}\",\n  \"typeId\": null,\n  \"fciId\": \"FCI_NEW_001\",\n  \"synchroId\": \"SYNC_NEW_001\",\n  \"obtainingYear\": 2024,\n  \"endDate\": \"2026-12-31\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"New AI Research Project\",\n      \"description\": \"New advanced AI research initiative\",\n      \"otherNames\": \"New AI Project\",\n      \"acronyms\": \"NARP\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Nouveau Projet de Recherche IA\",\n      \"description\": \"Nouvelle initiative de recherche avancée en IA\",\n      \"otherNames\": \"Nouveau Projet IA\",\n      \"acronyms\": \"NPRI\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_funding_projects_post"}], "authentication": {}, "metaSortKey": -1716807900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_funding_projects_put", "parentId": "fld_0000000000000004", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/funding-projects/{{ _.funding_project_id }}", "name": "Update Funding Project", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"holderId\": \"{{ _.person_id }}\",\n  \"typeId\": null,\n  \"fciId\": \"FCI_UPDATED_001\",\n  \"synchroId\": \"SYNC_UPDATED_001\",\n  \"obtainingYear\": 2025,\n  \"endDate\": \"2027-12-31\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Updated AI Research Project\",\n      \"description\": \"Updated advanced AI research initiative\",\n      \"otherNames\": \"Updated AI Project\",\n      \"acronyms\": \"UARP\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Projet de Recherche IA Mis à Jour\",\n      \"description\": \"Initiative de recherche avancée en IA mise à jour\",\n      \"otherNames\": \"Projet IA Mis à Jour\",\n      \"acronyms\": \"PRIMAJ\"\n    }\n  ]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_funding_projects_put"}], "authentication": {}, "metaSortKey": -1716807800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_funding_projects_delete", "parentId": "fld_0000000000000004", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/funding-projects/{{ _.funding_project_id }}", "name": "Delete Funding Project", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716807700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000005", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Institutions", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716807000000, "_type": "request_group"}, {"_id": "req_institutions_get", "parentId": "fld_0000000000000005", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/institutions", "name": "Get Institutions", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716807000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store institutions list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('institutions_list', response);\n    insomnia.environment.set('institution_id', response[0].id);\n    console.log('Institutions list stored:', response.length, 'items');\n    console.log('First institution ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No institutions found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_institutions_get_by_id", "parentId": "fld_0000000000000005", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/institutions/{{ _.institution_id }}", "name": "Get Institution by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716806500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_institutions_post", "parentId": "fld_0000000000000005", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/institutions", "name": "Create Institution", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"guidId\": \"guid_123\",\n  \"typeId\": \"institution_type_001\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"University of Montreal\",\n      \"description\": \"Leading research university\",\n      \"otherNames\": \"UdeM\",\n      \"acronyms\": \"UDM\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Universite de Montreal\",\n      \"description\": \"Universite de recherche de premier plan\",\n      \"otherNames\": \"UdeM\",\n      \"acronyms\": \"UDM\"\n    }\n  ],\n  \"modifiedBy\": \"user_123\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_institutions_post"}], "authentication": {}, "metaSortKey": -1716806900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_institutions_put", "parentId": "fld_0000000000000005", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/institutions/{{ _.institution_id }}", "name": "Update Institution", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"guidId\": \"guid_456\",\n  \"typeId\": \"institution_type_002\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Updated University of Montreal\",\n      \"description\": \"Updated leading research university\",\n      \"otherNames\": \"Updated UdeM\",\n      \"acronyms\": \"UUDM\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Universite de Montreal Mise a Jour\",\n      \"description\": \"Universite de recherche de premier plan mise a jour\",\n      \"otherNames\": \"UdeM Mise a Jour\",\n      \"acronyms\": \"UDMMAJ\"\n    }\n  ],\n  \"modifiedBy\": \"user_456\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_institutions_put"}], "authentication": {}, "metaSortKey": -1716806800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_institutions_delete", "parentId": "fld_0000000000000005", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/institutions/{{ _.institution_id }}", "name": "Delete Institution", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716806700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000006", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "People", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716806000000, "_type": "request_group"}, {"_id": "req_people_get", "parentId": "fld_0000000000000006", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/people", "name": "Get People", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716806000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store people list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('people_list', response);\n    insomnia.environment.set('person_id', response[0].id);\n    console.log('People list stored:', response.length, 'items');\n    console.log('First person ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No people found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_people_get_by_id", "parentId": "fld_0000000000000006", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/people/{{ _.person_id }}", "name": "Get Person by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716805500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_people_post", "parentId": "fld_0000000000000006", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/people", "name": "Create Person", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"guidId\": \"person_guid_123\",\n  \"uid\": \"john.doe\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Do<PERSON>\",\n  \"userId\": \"user_123\",\n  \"modifiedBy\": \"admin_123\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_people_post"}], "authentication": {}, "metaSortKey": -1716805900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_people_put", "parentId": "fld_0000000000000006", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/people/{{ _.person_id }}", "name": "Update Person", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"guidId\": \"person_guid_456\",\n  \"uid\": \"jane.smith\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"userId\": \"user_456\",\n  \"modifiedBy\": \"admin_456\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_people_put"}], "authentication": {}, "metaSortKey": -1716805800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_people_delete", "parentId": "fld_0000000000000006", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/people/{{ _.person_id }}", "name": "Delete Person", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716805700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000007", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Rooms", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716805000000, "_type": "request_group"}, {"_id": "req_rooms_get", "parentId": "fld_0000000000000007", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/rooms", "name": "Get Rooms", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716805000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store rooms list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('rooms_list', response);\n    insomnia.environment.set('room_id', response[0].id);\n    console.log('Rooms list stored:', response.length, 'items');\n    console.log('First room ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No rooms found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_rooms_get_by_id", "parentId": "fld_0000000000000007", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/rooms/{{ _.room_id }}", "name": "Get Room by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716804500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_rooms_post", "parentId": "fld_0000000000000007", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/rooms", "name": "Create Room", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"number\": \"NEW-ROOM-001\",\n  \"area\": 25.5,\n  \"floorLoad\": null,\n  \"buildingId\": \"{{ _.building_id }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_rooms_post"}], "authentication": {}, "metaSortKey": -1716804900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_rooms_put", "parentId": "fld_0000000000000007", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/rooms/{{ _.room_id }}", "name": "Update Room", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"number\": \"UPDATED-ROOM-001\",\n  \"area\": 30.0,\n  \"floorLoad\": null,\n  \"buildingId\": \"{{ _.building_id }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_rooms_put"}], "authentication": {}, "metaSortKey": -1716804800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_rooms_delete", "parentId": "fld_0000000000000007", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/rooms/{{ _.room_id }}", "name": "Delete Room", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716804700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000008", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Units", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716804000000, "_type": "request_group"}, {"_id": "req_units_get", "parentId": "fld_0000000000000008", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/units", "name": "Get Units", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716804000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store units list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('units_list', response);\n    insomnia.environment.set('unit_id', response[0].id);\n    console.log('Units list stored:', response.length, 'items');\n    console.log('First unit ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No units found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_units_get_by_id", "parentId": "fld_0000000000000008", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/units/{{ _.unit_id }}", "name": "Get Unit by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716803500000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_units_post", "parentId": "fld_0000000000000008", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/units", "name": "Create Unit", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"sadId\": \"SAD_UNIT_001\",\n  \"parentId\": \"parent_unit_123\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Computer Science Department\",\n      \"description\": \"Department of Computer Science and Engineering\",\n      \"otherNames\": \"CS Dept\",\n      \"acronyms\": \"CSD\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Departement d'Informatique\",\n      \"description\": \"Departement d'informatique et de genie logiciel\",\n      \"otherNames\": \"Dept Info\",\n      \"acronyms\": \"DI\"\n    }\n  ],\n  \"modifiedBy\": \"admin_123\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_units_post"}], "authentication": {}, "metaSortKey": -1716803900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_units_put", "parentId": "fld_0000000000000008", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/units/{{ _.unit_id }}", "name": "Update Unit", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"sadId\": \"SAD_UNIT_002\",\n  \"parentId\": \"parent_unit_456\",\n  \"translations\": [\n    {\n      \"locale\": \"en\",\n      \"name\": \"Updated Computer Science Department\",\n      \"description\": \"Updated Department of Computer Science and Engineering\",\n      \"otherNames\": \"Updated CS Dept\",\n      \"acronyms\": \"UCSD\"\n    },\n    {\n      \"locale\": \"fr\",\n      \"name\": \"Departement d'Informatique Mis a Jour\",\n      \"description\": \"Departement d'informatique et de genie logiciel mis a jour\",\n      \"otherNames\": \"Dept Info Mis a Jour\",\n      \"acronyms\": \"DIMAJ\"\n    }\n  ],\n  \"modifiedBy\": \"admin_456\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_units_put"}], "authentication": {}, "metaSortKey": -1716803800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_units_delete", "parentId": "fld_0000000000000008", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/units/{{ _.unit_id }}", "name": "Delete Unit", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716803700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000009", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Vend<PERSON>", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716803000000, "_type": "request_group"}, {"_id": "req_vendors_get", "parentId": "fld_0000000000000009", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/vendors", "name": "Get Vendors", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716803000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Store vendors list', () => {\n  const response = insomnia.response.json();\n  if (response && Array.isArray(response) && response.length > 0) {\n    insomnia.environment.set('vendors_list', response);\n    insomnia.environment.set('vendor_id', response[0].id);\n    console.log('Vendors list stored:', response.length, 'items');\n    console.log('First vendor ID:', response[0].id);\n    insomnia.expect(response.length).toBeGreaterThan(0);\n  } else {\n    console.log('No vendors found or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_vendors_get_by_id", "parentId": "fld_0000000000000009", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/vendors/{{ _.vendor_id }}", "name": "Get V<PERSON>or by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716802950000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "tests": "insomnia.test('Vendor retrieved successfully', () => {\n  const response = insomnia.response.json();\n  if (response && response.id) {\n    console.log('Vendor retrieved successfully, ID:', response.id);\n    insomnia.expect(response.id).toBeTruthy();\n    insomnia.expect(response).toHaveProperty('id');\n  } else {\n    console.log('Failed to retrieve vendor or invalid response');\n    insomnia.expect(response).toBeTruthy();\n  }\n});", "_type": "request"}, {"_id": "req_vendors_post", "parentId": "fld_0000000000000009", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/vendors", "name": "Create <PERSON><PERSON><PERSON>", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Example Vendor\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"phone\": \"******-0123\",\n  \"address\": \"123 Vendor St\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_vendors_post"}], "authentication": {}, "metaSortKey": -1716802900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_vendors_put", "parentId": "fld_0000000000000009", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/vendors/{{ _.vendor_id }}", "name": "Update Vendor", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Updated Vendor Name\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"phone\": \"******-9876\",\n  \"address\": \"456 Updated Vendor Ave\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_vendors_put"}], "authentication": {}, "metaSortKey": -1716802800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_vendors_delete", "parentId": "fld_0000000000000009", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/vendors/{{ _.vendor_id }}", "name": "Delete Vendor", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716802700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000010", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Permissions", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716802000000, "_type": "request_group"}, {"_id": "req_permissions_get", "parentId": "fld_0000000000000010", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permissions", "name": "Get Permissions", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716802000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permissions_post", "parentId": "fld_0000000000000010", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permissions", "name": "Create Permission", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"read_users\",\n  \"description\": \"Permission to read user data\",\n  \"resource\": \"users\",\n  \"action\": \"read\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_permissions_post"}], "authentication": {}, "metaSortKey": -1716801900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permissions_put", "parentId": "fld_0000000000000010", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permissions/1", "name": "Update Permission", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"write_users\",\n  \"description\": \"Permission to write user data\",\n  \"resource\": \"users\",\n  \"action\": \"write\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_permissions_put"}], "authentication": {}, "metaSortKey": -1716801800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permissions_delete", "parentId": "fld_0000000000000010", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permissions/1", "name": "Delete Permission", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716801700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000011", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Permission Groups", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716801000000, "_type": "request_group"}, {"_id": "req_permission_groups_get", "parentId": "fld_0000000000000011", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permission-groups", "name": "Get Permission Groups", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716801000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permission_groups_post", "parentId": "fld_0000000000000011", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permission-groups", "name": "Create Permission Group", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"User Management\",\n  \"description\": \"Group for user management permissions\",\n  \"permissions\": [1, 2, 3]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_permission_groups_post"}], "authentication": {}, "metaSortKey": -1716800900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permission_groups_put", "parentId": "fld_0000000000000011", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permission-groups/1", "name": "Update Permission Group", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Updated User Management\",\n  \"description\": \"Updated group for user management permissions\",\n  \"permissions\": [1, 2, 3, 4]\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_permission_groups_put"}], "authentication": {}, "metaSortKey": -1716800800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_permission_groups_delete", "parentId": "fld_0000000000000011", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/permission-groups/1", "name": "Delete Permission Group", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716800700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_0000000000000012", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Service Offers", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1716800000000, "_type": "request_group"}, {"_id": "req_service_offers_get", "parentId": "fld_0000000000000012", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/service-offers", "name": "Get Service Offers", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716800000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_service_offers_post", "parentId": "fld_0000000000000012", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/service-offers", "name": "Create Service Offer", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"title\": \"Example Service\",\n  \"description\": \"Description of the service offered\",\n  \"category\": \"consulting\",\n  \"price\": 100.00\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_service_offers_post"}], "authentication": {}, "metaSortKey": -1716799900000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_service_offers_put", "parentId": "fld_0000000000000012", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/service-offers/1", "name": "Update Service Offer", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"title\": \"Updated Service Title\",\n  \"description\": \"Updated description of the service\",\n  \"category\": \"development\",\n  \"price\": 150.00\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_service_offers_put"}], "authentication": {}, "metaSortKey": -1716799800000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_service_offers_delete", "parentId": "fld_0000000000000012", "modified": 1716811200000, "created": 1716811200000, "url": "{{ _.base_url }}/api/v2/service-offers/1", "name": "Delete Service Offer", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1716799700000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_0000000000000001", "parentId": "wrk_0000000000000001", "modified": 1716811200000, "created": 1716811200000, "name": "Base Environment", "data": {"base_url": "http://localhost:4000", "email": "<EMAIL>", "password": "patates19", "buildings_list": [{"id": "bw6c4rifj8239sc6gh<PERSON>up<PERSON>", "campusId": "sugfz9s755ypvxe28y7xwcgf", "civicAddressId": null, "sadId": "504A", "diId": null, "createdAt": "2025-05-26 18:58:43.366455+00", "updatedAt": "2025-05-26 18:58:43.366455+00", "modifiedBy": null, "translations": [{"id": "ix2d0zx59plz7lq4ejjud4kw", "locale": "fr", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "otherNames": null}]}], "campuses_list": [{"id": "sugfz9s755ypvxe28y7xwcgf", "sadId": null, "institutionId": "sydsapwdh5p2m9z9tvnde2dp", "createdAt": "2025-05-26 18:58:43.363592+00", "updatedAt": "2025-05-26 18:58:43.363592+00", "modifiedBy": null, "translations": [{"id": "rh9gdyh0kf9ixeoauzpy4fxf", "locale": "fr", "name": "Campus principal"}]}], "funding_projects_list": [], "institutions_list": [{"id": "sydsapwdh5p2m9z9tvnde2dp", "guidId": "tq5t83q97zcwc3g2szk11qx4", "typeId": "bqmr7x60br0e6br1rw81uz4j", "createdAt": "2025-05-26 18:58:41.955119+00", "updatedAt": "2025-05-26 18:58:41.955119+00", "modifiedBy": null, "translations": [{"id": "da32lkkil4zawbnf01ydzym4", "locale": "fr", "name": "Université de Montréal", "description": null, "acronyms": null, "otherNames": null}, {"id": "pgerfu7o55jgve38llndro2z", "locale": "en", "name": "University of Montreal", "description": null, "acronyms": null, "otherNames": null}]}], "people_list": [{"id": "w4ku5n46mdvkv5bjuuml2j3w", "guidId": "vfujoprun6tyny6cjcib35y6", "uid": "in13580", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "userId": null, "createdAt": "2025-05-26 18:58:41.838442+00", "updatedAt": "2025-05-26 18:58:41.838442+00", "modifiedBy": null}], "rooms_list": [{"id": "vot1nira0alg10houmycxefw", "number": "E-310-2", "area": 6.83, "floorLoad": null, "buildingId": "eyde7vewby0a0iwko2aejhpc", "createdAt": "2025-05-26 18:58:43.389869+00", "updatedAt": "2025-05-26 18:58:43.389869+00", "modifiedBy": null, "building": {"id": "eyde7vewby0a0iwko2aejhpc"}}], "units_list": [{"id": "a7gxf8y310awt7qfadlkq311", "guidId": "uu3r8azxfgljop9snuoid0jr", "typeId": "za0617oxo85reouxame85qwi", "parentId": "arbdkhl8vmm535nhxfz3qpzt", "createdAt": "2025-05-26 18:58:41.974715+00", "updatedAt": "2025-05-26 18:58:41.974715+00", "modifiedBy": null, "translations": [{"id": "x7tqyd0wco1cjts71huqnnpl", "dataId": "a7gxf8y310awt7qfadlkq311", "locale": "fr", "name": "Institut de recherches cliniques de Montréal", "description": null, "otherNames": null, "acronyms": "IRCM"}]}], "vendors_list": [{"id": "wjbwu31n6ifz8ijm7g7v2b2m", "startDate": null, "endDate": null, "createdAt": "2025-05-26 18:58:41.959196+00", "updatedAt": "2025-05-26 18:58:41.959196+00", "modifiedBy": null, "translations": [{"id": "la8bigpd66j42023vx61fcln", "locale": "fr", "name": "3E Company", "description": null, "otherNames": null}]}], "building_id": "bw6c4rifj8239sc6gh<PERSON>up<PERSON>", "campus_id": "sugfz9s755ypvxe28y7xwcgf", "funding_project_id": "", "institution_id": "sydsapwdh5p2m9z9tvnde2dp", "person_id": "w4ku5n46mdvkv5bjuuml2j3w", "room_id": "vot1nira0alg10houmycxefw", "unit_id": "a7gxf8y310awt7qfadlkq311", "vendor_id": "wjbwu31n6ifz8ijm7g7v2b2m"}, "dataPropertyOrder": {"&": ["base_url", "email", "password", "buildings_list", "campuses_list", "funding_projects_list", "institutions_list", "people_list", "rooms_list", "units_list", "vendors_list", "building_id", "campus_id", "funding_project_id", "institution_id", "person_id", "room_id", "unit_id", "vendor_id"]}, "color": null, "isPrivate": false, "metaSortKey": 1716811200000, "_type": "environment"}]}