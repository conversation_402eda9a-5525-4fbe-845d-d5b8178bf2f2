import type { PeopleRawSchema } from '@/schemas';
import { PeopleId } from '@/schemas';
import * as Either from 'effect/Either';
import * as Schema from 'effect/Schema';
import { describe, expect, it } from 'vitest';
import { PeopleRawToPeopleListViewDecoder } from './people.transformer';

describe('people.schema', () => {
  describe('PeopleRawToPeopleListViewDecoder', () => {
    it('should decode PeopleRawSchema to PeopleListViewSchema', () => {
      const rawPerson = {
        id: '123',
        familyName: 'Doe',
        givenName: '<PERSON>',
        personEmails: ['<EMAIL>', '<EMAIL>'],
      };

      const result = Schema.decodeEither(PeopleRawToPeopleListViewDecoder)(
        rawPerson,
      );

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          id: 123,
          familyName: 'Doe',
          givenName: '<PERSON>',
          emails: ['<EMAIL>', '<EMAIL>'],
        });
      }
    });

    it('should encode PeopleListViewSchema back to PeopleRawSchema', () => {
      const listViewPerson = {
        id: Schema.decodeSync(PeopleId)('456'),
        familyName: 'Smith',
        givenName: 'Jane',
        emails: ['<EMAIL>'],
      };

      // Act
      const result = Schema.encodeEither(PeopleRawToPeopleListViewDecoder)(
        listViewPerson,
      );

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          id: 456,
          familyName: 'Smith',
          givenName: 'Jane',
          personEmails: ['<EMAIL>'],
        });
      }
    });

    it('should fail to decode invalid PeopleRawSchema', () => {
      const invalidRawPerson = {
        id: '123',
        familyName: 'Doe',
        givenName: 'John',
        personEmails: [],
      } as Schema.Schema.Type<typeof PeopleRawSchema>;

      const result = Schema.decodeEither(PeopleRawToPeopleListViewDecoder)(
        invalidRawPerson,
      );

      expect(Either.isLeft(result)).toBe(true);
    });
  });
});
