import * as fs from 'node:fs/promises';
import { BaseConverter } from '@/migration-scripts/base-converter';
import { DbUtils } from '@rie/utils';

export class RolesMigrationConverter extends BaseConverter {
  private roleIdMappings: Record<string, string> = {};

  async convertFile(outputPath: string): Promise<void> {
    const roles = [
      'administrator',
      'super-editor',
      'super-explorer',
      'user',
      'visitor',
      'institution-manager',
      'institution-editor',
      'institution-explorer',
      'unit-manager',
      'unit-editor',
      'unit-explorer',
      'infrastructure-manager',
      'infrastructure-editor',
      'infrastructure-explorer',
    ];

    // The SQL content to append
    let rolesInsertStatement = 'INSERT INTO roles (id, name) VALUES\n';

    let index = 0;
    for (const role of roles) {
      const roleId = DbUtils.cuid2();
      rolesInsertStatement += `('${roleId}', '${role}')${index === roles.length - 1 ? ';' : ',\n'}`;
      index++;
      this.roleIdMappings[role] = roleId;
    }

    // Create the output directory if it doesn't exist
    const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
    await fs.mkdir(outputDir, { recursive: true });

    await this.writeMappingsToJson(
      outputDir,
      { mysql: 'roles', postgres: 'roles' },
      this.roleIdMappings,
    );

    // Append the SQL content to the output file
    await fs.appendFile(outputPath, rolesInsertStatement);
    console.log('Roles data added successfully!');
  }
}
