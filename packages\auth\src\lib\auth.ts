import { env } from '@/env';
import { db } from '@/lib/drizzle-client';
import {
  assignRoleToUser,
  getRoleByName,
  getUserRoles,
} from '@/queries/common';
import * as DbSchema from '@rie/db-schema/schemas';
import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { createAuthMiddleware } from 'better-auth/api';
import { customSession } from 'better-auth/plugins';
import { openAPI } from 'better-auth/plugins';

export const auth = betterAuth({
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      if (ctx.context.newSession) {
        const userRoles = await getUserRoles(ctx.context.newSession.user.id);
        if (!userRoles.find((role) => role.role.name === 'User')) {
          const baseUserRole = await getRoleByName('User');
          if (baseUserRole) {
            await assignRoleToUser(
              ctx.context.newSession.user.id,
              baseUserRole.id,
            );
          }
        }
      }
    }),
  },
  baseURL: env.NEXT_PUBLIC_API_BASE_URL.replace('/api', ''),
  basePath: '/api/v2/auth',
  database: drizzleAdapter(db, {
    provider: 'pg',
    schema: {
      account: DbSchema.accounts,
      session: DbSchema.sessions,
      user: DbSchema.users,
      verification: DbSchema.verifications,
    },
  }),
  emailAndPassword: {
    enabled: true,
  },
  secret: env.BETTER_AUTH_SECRET,
  socialProviders: {
    ...(process.env.NODE_ENV === 'development'
      ? {
          github: {
            enabled: true,
            clientId: env.GITHUB_CLIENT_ID,
            clientSecret: env.GITHUB_CLIENT_SECRET,
          },
        }
      : {
          microsoft: {
            enabled: true,
            clientId: env.MICROSOFT_CLIENT_ID,
            clientSecret: env.MICROSOFT_CLIENT_SECRET,
          },
        }),
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60, // 1 hour
    },
  },
  plugins: [
    openAPI(),
    customSession(async ({ user, session }) => {
      const roles = await getUserRoles(user.id);

      return {
        roles: roles.map((role) => role.role.name),
        user,
        session,
      };
    }),
  ],
  advanced: {
    cookiePrefix: 'rie',
    useSecureCookies: process.env.NODE_ENV === 'production',
    defaultCookieAttributes: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
    },
  },
  trustedOrigins: [env.NEXT_PUBLIC_ORIGIN_URL],
});
