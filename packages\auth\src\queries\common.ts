import { db } from '@/lib/drizzle-client';
import { roles, userRoles } from '@rie/db-schema/schemas';
import { eq } from 'drizzle-orm';

export const getRoleByName = async (roleName: string) => {
  return db.query.roles.findFirst({
    where: eq(roles.name, roleName),
    columns: {
      id: true,
      name: true,
    },
  });
};

export const assignRoleToUser = async (userId: string, roleId: string) => {
  return db.insert(userRoles).values({
    userId,
    roleId,
  });
};

export const getUserRoles = async (userId: string) => {
  return db.query.userRoles.findMany({
    where: eq(userRoles.userId, userId),
    columns: {
      roleId: true,
    },
    with: {
      role: {
        columns: {
          id: true,
          name: true,
        },
      },
    },
  });
};
