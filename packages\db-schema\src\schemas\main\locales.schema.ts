import { DbUtils } from '@rie/utils';
import { pgTable, text, unique } from 'drizzle-orm/pg-core';

export const locales = pgTable('locales', {
  code: text().primaryKey().notNull(),
});

export const localesI18N = pgTable(
  'locales_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => locales.code, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);
