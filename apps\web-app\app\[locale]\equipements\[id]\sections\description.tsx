'use client';

import { useDescriptionSections } from '@/app/[locale]/equipements/[id]/sections/hooks/useDescriptionSections';
import { DetailsSection } from '@/components/details-section/details-section';
import { Heading } from '@/components/ui/heading';
import type { EquipmentDescriptionDetailed } from '@/types/equipment';
import 'lightgallery/css/lg-thumbnail.css';
import 'lightgallery/css/lg-video.css';
import 'lightgallery/css/lg-zoom.css';
import 'lightgallery/css/lightgallery.css';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgVideo from 'lightgallery/plugins/video';
import lgZoom from 'lightgallery/plugins/zoom';
import LightGallery from 'lightgallery/react';
import { useEffect, useState } from 'react';

type DescriptionProps = {
  photos?: { caption: string; thumbnail: string; url: string }[];
  videos?: { caption: string; thumbnail: string; url: string }[];
} & EquipmentDescriptionDetailed;

export const Description = (props: DescriptionProps) => {
  const sections = useDescriptionSections(props);
  const [_preloadedImages, setPreloadedImages] = useState<boolean[]>([]);

  const images = props.photos || [];

  // Préchargement optionnel
  useEffect(() => {
    Promise.all(
      images.map(
        ({ url }) =>
          new Promise((res) => {
            const img = new Image();
            img.src = url;
            img.onload = () => res(true);
            img.onerror = () => res(false);
          }),
      ),
    ).then((states) => setPreloadedImages(states as boolean[]));
  }, [images]);

  return (
    <div className="flex flex-col gap-y-8">
      <DetailsSection sections={sections} />

      {images.length > 0 && (
        <div>
          <Heading level={2}>Images</Heading>
          <LightGallery
            speed={500}
            plugins={[lgThumbnail, lgZoom]}
            elementClassNames="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"
          >
            {images.map(({ url, thumbnail, caption }, index) => {
              const uniqueKey = `img-${caption.slice(0, 10).replace(/\s+/g, '-')}-${url.split('/').pop()?.split('.')[0] || index}`;
              return (
                <a
                  href={url}
                  key={uniqueKey}
                  data-sub-html={caption}
                  className="block aspect-[4/3] overflow-hidden rounded"
                >
                  <img
                    src={thumbnail}
                    alt={caption}
                    className="w-full h-full object-cover transition-transform hover:scale-105"
                    loading="lazy"
                  />
                </a>
              );
            })}
          </LightGallery>
        </div>
      )}

      {props.videos && props.videos.length > 0 && (
        <div>
          <Heading level={2}>Videos</Heading>
          <LightGallery
            speed={500}
            plugins={[lgThumbnail, lgZoom, lgVideo]}
            elementClassNames="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
          >
            {props.videos.map(({ url, thumbnail, caption }, index) => {
              const uniqueKey = `vid-${caption.slice(0, 10).replace(/\s+/g, '-')}-${url.split('/').pop()?.split('.')[0] || index}`;
              return (
                <a
                  href={url}
                  key={uniqueKey}
                  data-sub-html={caption}
                  data-poster={thumbnail}
                  className="block aspect-video overflow-hidden rounded"
                >
                  <img
                    src={thumbnail}
                    alt={caption}
                    className="w-full h-full object-cover transition-transform hover:scale-105"
                    loading="lazy"
                  />
                </a>
              );
            })}
          </LightGallery>
        </div>
      )}
    </div>
  );
};
