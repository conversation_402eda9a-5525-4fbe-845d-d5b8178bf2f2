import { DirectoryElementsPage } from '@/app/[locale]/bottin/directory-page/directory-page';
import { queryClientOptions } from '@/constants/query-client';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { entitiesListOptions } from '@/hooks/bottin/useGetEntitiesListOptions';
import type { DirectoryEntityKey } from '@/i18n/settings';
import type { SupportedLocale } from '@/types/locale';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';

type EntitiesPageProps = {
  locale: SupportedLocale;
  directoryEntity: DirectoryEntityKey;
};
export default async function EntitiesPage({
  locale,
  directoryEntity,
}: EntitiesPageProps) {
  const queryClient = new QueryClient(queryClientOptions);

  await queryClient.prefetchQuery(
    entitiesListOptions(
      0,
      defaultRieServiceParams(locale),
      '',
      directoryEntity,
    ),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <DirectoryElementsPage
        directoryEntity={directoryEntity}
        locale={locale}
      />
    </HydrationBoundary>
  );
}
