{"name": "@rie/auth", "version": "1.0.0", "type": "module", "description": "Authentication package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "dependencies": {"@rie/biome-config": "workspace:*", "@rie/db-schema": "workspace:*", "@rie/utils": "workspace:*", "@t3-oss/env-core": "^0.13.4", "better-auth": "^1.2.7", "drizzle-orm": "^0.43.1", "effect": "^3.15.0", "pg": "^8.16.0"}, "devDependencies": {"@types/node": "^22.15.17", "@types/pg": "^8.15.1", "tsc-alias": "^1.8.16", "typescript": "^5.8.3"}}