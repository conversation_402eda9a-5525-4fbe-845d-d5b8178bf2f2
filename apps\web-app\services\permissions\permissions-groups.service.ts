import type { PermissionsGroupFormSchema } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form.schema';
import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { DbPermissionGroup } from '@rie/db-schema/entity-types';

export const getAllPermissionsGroups = async () => {
  try {
    const client = await getApiClient();
    return await client.get<DbPermissionGroup[]>('v2/permission-groups').json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch permission groups');
  }
};

export async function createPermissionsGroup(
  payload: PermissionsGroupFormSchema,
): Promise<DbPermissionGroup> {
  try {
    const client = await getApiClient();

    return await client
      .post<DbPermissionGroup>('v2/permission-groups', {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }

    // Otherwise, wrap it in a generic error
    throw new Error('Failed to create permission group');
  }
}

interface UpdatePermissionsGroupParams {
  payload: PermissionsGroupFormSchema;
  id: string;
}

export const updatePermissionsGroup = async ({
  payload,
  id,
}: UpdatePermissionsGroupParams) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.put(`v2/permission-groups/${id}`, {
      json: payload,
    });
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    // Otherwise, wrap it in a generic error
    throw new Error('Failed to update permission group');
  }
};

export const deletePermissionsGroup = async (id: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(`v2/permission-groups/${id}`);
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }

    throw new Error('Failed to delete permission group');
  }
};
