'use client';

import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form.schema';
import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import {
  createRole,
  deleteRole,
  updateRole,
} from '@/services/permissions/roles.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAllRolesOptions, getRoleByIdOptions } from './roles.options';

export const useGetAllRoles = () => {
  return useQuery(getAllRolesOptions());
};

export const useGetRoleById = (id: string) => {
  return useQuery(getRoleByIdOptions(id));
};

export const useCreateRole = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (payload: RoleFormSchema) => {
      console.log('creating role with', payload);
      return await createRole(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role created successfully',
        variant: 'success',
      });
      router.push('/admin/roles');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create role',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateRole = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      payload,
      id,
    }: { payload: RoleFormSchema; id: string }) => {
      return await updateRole({ payload, id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role updated successfully',
        variant: 'success',
      });
      router.push('/admin/roles');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update role',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role deleted successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to delete role',
        variant: 'destructive',
      });
    },
  });
};
