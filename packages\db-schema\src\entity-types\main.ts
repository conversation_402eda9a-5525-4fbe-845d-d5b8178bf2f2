import type {
  // Address schemas
  addresses,
  // Application sectors
  applicationSectors,
  applicationSectorsI18N,
  // Buildings
  buildings,
  buildingsI18N,
  campusAddresses,
  // Campuses
  campuses,
  campusesI18N,
  civicAddresses,
  // Documentation
  documentationCategories,
  documentationCategoriesI18N,
  equipmentAssociatedApplicationSectors,
  // Equipment schemas
  equipmentAssociatedCategories,
  equipmentAssociatedDimensions,
  equipmentAssociatedDocuments,
  equipmentAssociatedExcellenceHubs,
  equipmentAssociatedFundingProjects,
  equipmentAssociatedLifecycles,
  equipmentAssociatedMaintenances,
  equipmentAssociatedMedia,
  equipmentAssociatedOperationalManagers,
  equipmentAssociatedRepairers,
  equipmentAssociatedResearchFields,
  equipmentAssociatedRetailers,
  equipmentAssociatedSSTManagers,
  equipmentAssociatedTechniques,
  equipmentCategories,
  equipmentCategoriesI18N,
  equipmentRelationshipTypes,
  equipmentRelationshipTypesI18N,
  equipmentRelationships,
  equipmentRelationshipsI18N,
  equipmentStatuses,
  equipmentStatusesI18N,
  equipmentTypes,
  equipmentTypesI18N,
  equipments,
  equipmentsI18N,
  // Excellence hubs
  excellenceHubs,
  excellenceHubsI18N,
  // Funding projects
  fundingProjectAssociatedIdentifiers,
  fundingProjectAssociatedPeople,
  fundingProjectIdentifierTypes,
  fundingProjectIdentifierTypesI18N,
  fundingProjectInfrastructures,
  fundingProjectTypes,
  fundingProjectTypesI18N,
  fundingProjects,
  fundingProjectsI18N,
  // GUIDs
  guids,
  infrastructureAssociatedInnovationLabs,
  // Infrastructures
  infrastructureAssociatedPeople,
  infrastructureAssociatedScientificManagers,
  infrastructureStatuses,
  infrastructureStatusesI18N,
  infrastructureTypes,
  infrastructureTypesI18N,
  infrastructures,
  infrastructuresI18N,
  // Innovation labs
  innovationLabs,
  innovationLabsI18N,
  // Institutions
  institutionAssociatedPeople,
  institutionRoleTypes,
  institutionRoleTypesI18N,
  institutionTypes,
  institutionTypesI18N,
  institutions,
  institutionsI18N,
  // Locales
  locales,
  // Media
  media,
  mediaI18N,
  mediaTypes,
  mediaTypesI18N,
  // People
  people,
  peopleAssociatedEmails,
  peopleAssociatedMedia,
  peopleAssociatedPhones,
  peopleRoleTypes,
  peopleRoleTypesI18N,
  // Research fields
  researchFields,
  researchFieldsI18N,
  roomAssociatedCategories,
  // Rooms
  roomCategories,
  roomCategoriesI18N,
  rooms,
  // Service contracts
  serviceContracts,
  serviceContractsI18N,
  // Service offers
  serviceOffers,
  serviceOffersEquipments,
  // Techniques
  techniques,
  techniquesI18N,
  timeUnits,
  timeUnitsI18N,
  // Units
  unitAssociatedPeople,
  unitParents,
  unitTypes,
  unitTypesI18N,
  units,
  unitsI18N,
  // Vendors
  vendors,
  vendorsI18N,
  // Visibilities
  visibilities,
  visibilitiesI18N,
} from '@/schemas';

import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';

// Address types
export type DbAddress = InferSelectModel<typeof addresses>;
export type DbAddressInput = Omit<InferInsertModel<typeof addresses>, 'id'>;
export type DbCampusAddress = InferSelectModel<typeof campusAddresses>;
export type DbCampusAddressInput = InferInsertModel<typeof campusAddresses>;
export type DbCivicAddress = InferSelectModel<typeof civicAddresses>;
export type DbCivicAddressInput = Omit<
  InferInsertModel<typeof civicAddresses>,
  'id'
>;

// Application sector types
export type DbApplicationSector = InferSelectModel<typeof applicationSectors>;
export type DbApplicationSectorInput = Omit<
  InferInsertModel<typeof applicationSectors>,
  'id'
>;
export type DbApplicationSectorI18N = InferSelectModel<
  typeof applicationSectorsI18N
>;
export type DbApplicationSectorI18NInput = InferInsertModel<
  typeof applicationSectorsI18N
>;
export type DbEquipmentAssociatedApplicationSector = InferSelectModel<
  typeof equipmentAssociatedApplicationSectors
>;
export type DbEquipmentAssociatedApplicationSectorInput = InferInsertModel<
  typeof equipmentAssociatedApplicationSectors
>;

// Building types
export type DbBuilding = InferSelectModel<typeof buildings>;
export type DbBuildingInput = Omit<InferInsertModel<typeof buildings>, 'id'>;
export type DbBuildingI18N = InferSelectModel<typeof buildingsI18N>;
export type DbBuildingI18NInput = InferInsertModel<typeof buildingsI18N>;

// Campus types
export type DbCampus = InferSelectModel<typeof campuses>;
export type DbCampusInput = Omit<InferInsertModel<typeof campuses>, 'id'>;
export type DbCampusI18N = InferSelectModel<typeof campusesI18N>;
export type DbCampusI18NInput = InferInsertModel<typeof campusesI18N>;

// Documentation types
export type DbDocumentationCategory = InferSelectModel<
  typeof documentationCategories
>;
export type DbDocumentationCategoryInput = Omit<
  InferInsertModel<typeof documentationCategories>,
  'id'
>;
export type DbDocumentationCategoryI18N = InferSelectModel<
  typeof documentationCategoriesI18N
>;
export type DbDocumentationCategoryI18NInput = InferInsertModel<
  typeof documentationCategoriesI18N
>;

// Equipment types
export type DbEquipment = InferSelectModel<typeof equipments>;
export type DbEquipmentInput = Omit<InferInsertModel<typeof equipments>, 'id'>;
export type DbEquipmentI18N = InferSelectModel<typeof equipmentsI18N>;
export type DbEquipmentI18NInput = InferInsertModel<typeof equipmentsI18N>;
export type DbEquipmentCategory = InferSelectModel<typeof equipmentCategories>;
export type DbEquipmentCategoryInput = Omit<
  InferInsertModel<typeof equipmentCategories>,
  'id'
>;
export type DbEquipmentCategoryI18N = InferSelectModel<
  typeof equipmentCategoriesI18N
>;
export type DbEquipmentCategoryI18NInput = InferInsertModel<
  typeof equipmentCategoriesI18N
>;
export type DbEquipmentType = InferSelectModel<typeof equipmentTypes>;
export type DbEquipmentTypeInput = Omit<
  InferInsertModel<typeof equipmentTypes>,
  'id'
>;
export type DbEquipmentTypeI18N = InferSelectModel<typeof equipmentTypesI18N>;
export type DbEquipmentTypeI18NInput = InferInsertModel<
  typeof equipmentTypesI18N
>;
export type DbEquipmentStatus = InferSelectModel<typeof equipmentStatuses>;
export type DbEquipmentStatusInput = Omit<
  InferInsertModel<typeof equipmentStatuses>,
  'id'
>;
export type DbEquipmentStatusI18N = InferSelectModel<
  typeof equipmentStatusesI18N
>;
export type DbEquipmentStatusI18NInput = InferInsertModel<
  typeof equipmentStatusesI18N
>;
export type DbEquipmentAssociatedCategory = InferSelectModel<
  typeof equipmentAssociatedCategories
>;
export type DbEquipmentAssociatedCategoryInput = InferInsertModel<
  typeof equipmentAssociatedCategories
>;
export type DbEquipmentAssociatedTechnique = InferSelectModel<
  typeof equipmentAssociatedTechniques
>;
export type DbEquipmentAssociatedTechniqueInput = InferInsertModel<
  typeof equipmentAssociatedTechniques
>;
export type DbEquipmentAssociatedMedia = InferSelectModel<
  typeof equipmentAssociatedMedia
>;
export type DbEquipmentAssociatedMediaInput = InferInsertModel<
  typeof equipmentAssociatedMedia
>;
export type DbEquipmentAssociatedDimension = InferSelectModel<
  typeof equipmentAssociatedDimensions
>;
export type DbEquipmentAssociatedDimensionInput = InferInsertModel<
  typeof equipmentAssociatedDimensions
>;
export type DbEquipmentAssociatedLifecycle = InferSelectModel<
  typeof equipmentAssociatedLifecycles
>;
export type DbEquipmentAssociatedLifecycleInput = InferInsertModel<
  typeof equipmentAssociatedLifecycles
>;
export type DbEquipmentAssociatedMaintenance = InferSelectModel<
  typeof equipmentAssociatedMaintenances
>;
export type DbEquipmentAssociatedMaintenanceInput = InferInsertModel<
  typeof equipmentAssociatedMaintenances
>;
export type DbEquipmentAssociatedOperationalManager = InferSelectModel<
  typeof equipmentAssociatedOperationalManagers
>;
export type DbEquipmentAssociatedOperationalManagerInput = InferInsertModel<
  typeof equipmentAssociatedOperationalManagers
>;
export type DbEquipmentAssociatedSSTManager = InferSelectModel<
  typeof equipmentAssociatedSSTManagers
>;
export type DbEquipmentAssociatedSSTManagerInput = InferInsertModel<
  typeof equipmentAssociatedSSTManagers
>;
export type DbEquipmentAssociatedRepairer = InferSelectModel<
  typeof equipmentAssociatedRepairers
>;
export type DbEquipmentAssociatedRepairerInput = InferInsertModel<
  typeof equipmentAssociatedRepairers
>;
export type DbEquipmentAssociatedRetailer = InferSelectModel<
  typeof equipmentAssociatedRetailers
>;
export type DbEquipmentAssociatedRetailerInput = InferInsertModel<
  typeof equipmentAssociatedRetailers
>;
export type DbEquipmentAssociatedExcellenceHub = InferSelectModel<
  typeof equipmentAssociatedExcellenceHubs
>;
export type DbEquipmentAssociatedExcellenceHubInput = InferInsertModel<
  typeof equipmentAssociatedExcellenceHubs
>;
export type DbEquipmentAssociatedFundingProject = InferSelectModel<
  typeof equipmentAssociatedFundingProjects
>;
export type DbEquipmentAssociatedFundingProjectInput = InferInsertModel<
  typeof equipmentAssociatedFundingProjects
>;
export type DbEquipmentRelationship = InferSelectModel<
  typeof equipmentRelationships
>;
export type DbEquipmentRelationshipInput = Omit<
  InferInsertModel<typeof equipmentRelationships>,
  'id'
>;
export type DbEquipmentRelationshipI18N = InferSelectModel<
  typeof equipmentRelationshipsI18N
>;
export type DbEquipmentRelationshipI18NInput = InferInsertModel<
  typeof equipmentRelationshipsI18N
>;
export type DbEquipmentRelationshipType = InferSelectModel<
  typeof equipmentRelationshipTypes
>;
export type DbEquipmentRelationshipTypeInput = Omit<
  InferInsertModel<typeof equipmentRelationshipTypes>,
  'id'
>;
export type DbEquipmentRelationshipTypeI18N = InferSelectModel<
  typeof equipmentRelationshipTypesI18N
>;
export type DbEquipmentRelationshipTypeI18NInput = InferInsertModel<
  typeof equipmentRelationshipTypesI18N
>;
export type DbTimeUnit = InferSelectModel<typeof timeUnits>;
export type DbTimeUnitInput = Omit<InferInsertModel<typeof timeUnits>, 'id'>;
export type DbTimeUnitI18N = InferSelectModel<typeof timeUnitsI18N>;
export type DbTimeUnitI18NInput = InferInsertModel<typeof timeUnitsI18N>;

// Excellence hub types
export type DbExcellenceHub = InferSelectModel<typeof excellenceHubs>;
export type DbExcellenceHubInput = Omit<
  InferInsertModel<typeof excellenceHubs>,
  'id'
>;
export type DbExcellenceHubI18N = InferSelectModel<typeof excellenceHubsI18N>;
export type DbExcellenceHubI18NInput = InferInsertModel<
  typeof excellenceHubsI18N
>;

// Funding project types
export type DbFundingProject = InferSelectModel<typeof fundingProjects>;
export type DbFundingProjectInput = Omit<
  InferInsertModel<typeof fundingProjects>,
  'id'
>;
export type DbFundingProjectI18N = InferSelectModel<typeof fundingProjectsI18N>;
export type DbFundingProjectI18NInput = InferInsertModel<
  typeof fundingProjectsI18N
>;
export type DbFundingProjectType = InferSelectModel<typeof fundingProjectTypes>;
export type DbFundingProjectTypeInput = Omit<
  InferInsertModel<typeof fundingProjectTypes>,
  'id'
>;
export type DbFundingProjectTypeI18N = InferSelectModel<
  typeof fundingProjectTypesI18N
>;
export type DbFundingProjectTypeI18NInput = InferInsertModel<
  typeof fundingProjectTypesI18N
>;
export type DbFundingProjectIdentifierType = InferSelectModel<
  typeof fundingProjectIdentifierTypes
>;
export type DbFundingProjectIdentifierTypeInput = Omit<
  InferInsertModel<typeof fundingProjectIdentifierTypes>,
  'id'
>;
export type DbFundingProjectIdentifierTypeI18N = InferSelectModel<
  typeof fundingProjectIdentifierTypesI18N
>;
export type DbFundingProjectIdentifierTypeI18NInput = InferInsertModel<
  typeof fundingProjectIdentifierTypesI18N
>;
export type DbFundingProjectAssociatedIdentifier = InferSelectModel<
  typeof fundingProjectAssociatedIdentifiers
>;
export type DbFundingProjectAssociatedIdentifierInput = InferInsertModel<
  typeof fundingProjectAssociatedIdentifiers
>;
export type DbFundingProjectAssociatedPerson = InferSelectModel<
  typeof fundingProjectAssociatedPeople
>;
export type DbFundingProjectAssociatedPersonInput = InferInsertModel<
  typeof fundingProjectAssociatedPeople
>;
export type DbFundingProjectInfrastructure = InferSelectModel<
  typeof fundingProjectInfrastructures
>;
export type DbFundingProjectInfrastructureInput = InferInsertModel<
  typeof fundingProjectInfrastructures
>;

// GUID types
export type DbGuid = InferSelectModel<typeof guids>;
export type DbGuidInput = Omit<InferInsertModel<typeof guids>, 'id'>;

// Infrastructure types
export type DbInfrastructure = InferSelectModel<typeof infrastructures>;
export type DbInfrastructureInput = Omit<
  InferInsertModel<typeof infrastructures>,
  'id'
>;
export type DbInfrastructureI18N = InferSelectModel<typeof infrastructuresI18N>;
export type DbInfrastructureI18NInput = InferInsertModel<
  typeof infrastructuresI18N
>;
export type DbInfrastructureType = InferSelectModel<typeof infrastructureTypes>;
export type DbInfrastructureTypeInput = Omit<
  InferInsertModel<typeof infrastructureTypes>,
  'id'
>;
export type DbInfrastructureTypeI18N = InferSelectModel<
  typeof infrastructureTypesI18N
>;
export type DbInfrastructureTypeI18NInput = InferInsertModel<
  typeof infrastructureTypesI18N
>;
export type DbInfrastructureStatus = InferSelectModel<
  typeof infrastructureStatuses
>;
export type DbInfrastructureStatusInput = Omit<
  InferInsertModel<typeof infrastructureStatuses>,
  'id'
>;
export type DbInfrastructureStatusI18N = InferSelectModel<
  typeof infrastructureStatusesI18N
>;
export type DbInfrastructureStatusI18NInput = InferInsertModel<
  typeof infrastructureStatusesI18N
>;
export type DbInfrastructureAssociatedPerson = InferSelectModel<
  typeof infrastructureAssociatedPeople
>;
export type DbInfrastructureAssociatedPersonInput = InferInsertModel<
  typeof infrastructureAssociatedPeople
>;
export type DbInfrastructureAssociatedScientificManager = InferSelectModel<
  typeof infrastructureAssociatedScientificManagers
>;
export type DbInfrastructureAssociatedScientificManagerInput = InferInsertModel<
  typeof infrastructureAssociatedScientificManagers
>;

// Innovation lab types
export type DbInnovationLab = InferSelectModel<typeof innovationLabs>;
export type DbInnovationLabInput = Omit<
  InferInsertModel<typeof innovationLabs>,
  'id'
>;
export type DbInnovationLabI18N = InferSelectModel<typeof innovationLabsI18N>;
export type DbInnovationLabI18NInput = InferInsertModel<
  typeof innovationLabsI18N
>;

// Institution types
export type DbInstitution = InferSelectModel<typeof institutions>;
export type DbInstitutionInput = Omit<
  InferInsertModel<typeof institutions>,
  'id'
>;
export type DbInstitutionI18N = InferSelectModel<typeof institutionsI18N>;
export type DbInstitutionI18NInput = InferInsertModel<typeof institutionsI18N>;
export type DbInstitutionType = InferSelectModel<typeof institutionTypes>;
export type DbInstitutionTypeInput = Omit<
  InferInsertModel<typeof institutionTypes>,
  'id'
>;
export type DbInstitutionTypeI18N = InferSelectModel<
  typeof institutionTypesI18N
>;
export type DbInstitutionTypeI18NInput = InferInsertModel<
  typeof institutionTypesI18N
>;
export type DbInstitutionAssociatedPerson = InferSelectModel<
  typeof institutionAssociatedPeople
>;
export type DbInstitutionAssociatedPersonInput = InferInsertModel<
  typeof institutionAssociatedPeople
>;
export type DbInstitutionRoleType = InferSelectModel<
  typeof institutionRoleTypes
>;
export type DbInstitutionRoleTypeInput = Omit<
  InferInsertModel<typeof institutionRoleTypes>,
  'id'
>;
export type DbInstitutionRoleTypeI18N = InferSelectModel<
  typeof institutionRoleTypesI18N
>;
export type DbInstitutionRoleTypeI18NInput = InferInsertModel<
  typeof institutionRoleTypesI18N
>;

// Locale types
export type DbLocale = InferSelectModel<typeof locales>;
export type DbLocaleInput = InferInsertModel<typeof locales>;

// Media types
export type DbMedia = InferSelectModel<typeof media>;
export type DbMediaInput = Omit<InferInsertModel<typeof media>, 'id'>;
export type DbMediaI18N = InferSelectModel<typeof mediaI18N>;
export type DbMediaI18NInput = InferInsertModel<typeof mediaI18N>;
export type DbMediaType = InferSelectModel<typeof mediaTypes>;
export type DbMediaTypeInput = Omit<InferInsertModel<typeof mediaTypes>, 'id'>;
export type DbMediaTypeI18N = InferSelectModel<typeof mediaTypesI18N>;
export type DbMediaTypeI18NInput = InferInsertModel<typeof mediaTypesI18N>;

// People types
export type DbPerson = InferSelectModel<typeof people>;
export type DbPersonInput = Omit<InferInsertModel<typeof people>, 'id'>;
export type DbPeopleAssociatedEmail = InferSelectModel<
  typeof peopleAssociatedEmails
>;
export type DbPeopleAssociatedEmailInput = InferInsertModel<
  typeof peopleAssociatedEmails
>;
export type DbPeopleAssociatedPhone = InferSelectModel<
  typeof peopleAssociatedPhones
>;
export type DbPeopleAssociatedPhoneInput = InferInsertModel<
  typeof peopleAssociatedPhones
>;
export type DbPeopleAssociatedMedia = InferSelectModel<
  typeof peopleAssociatedMedia
>;
export type DbPeopleAssociatedMediaInput = InferInsertModel<
  typeof peopleAssociatedMedia
>;
export type DbPeopleRoleType = InferSelectModel<typeof peopleRoleTypes>;
export type DbPeopleRoleTypeInput = Omit<
  InferInsertModel<typeof peopleRoleTypes>,
  'id'
>;
export type DbPeopleRoleTypeI18N = InferSelectModel<typeof peopleRoleTypesI18N>;
export type DbPeopleRoleTypeI18NInput = InferInsertModel<
  typeof peopleRoleTypesI18N
>;

// Research field types
export type DbResearchField = InferSelectModel<typeof researchFields>;
export type DbResearchFieldInput = Omit<
  InferInsertModel<typeof researchFields>,
  'id'
>;
export type DbResearchFieldI18N = InferSelectModel<typeof researchFieldsI18N>;
export type DbResearchFieldI18NInput = InferInsertModel<
  typeof researchFieldsI18N
>;

// Room types
export type DbRoom = InferSelectModel<typeof rooms>;
export type DbRoomInput = Omit<InferInsertModel<typeof rooms>, 'id'>;
export type DbRoomCategory = InferSelectModel<typeof roomCategories>;
export type DbRoomCategoryInput = Omit<
  InferInsertModel<typeof roomCategories>,
  'id'
>;
export type DbRoomCategoryI18N = InferSelectModel<typeof roomCategoriesI18N>;
export type DbRoomCategoryI18NInput = InferInsertModel<
  typeof roomCategoriesI18N
>;

// Service contract types
export type DbServiceContract = InferSelectModel<typeof serviceContracts>;
export type DbServiceContractInput = Omit<
  InferInsertModel<typeof serviceContracts>,
  'id'
>;
export type DbServiceContractI18N = InferSelectModel<
  typeof serviceContractsI18N
>;
export type DbServiceContractI18NInput = InferInsertModel<
  typeof serviceContractsI18N
>;

// Service offer types
export type DbServiceOffer = InferSelectModel<typeof serviceOffers>;
export type DbServiceOfferInput = Omit<
  InferInsertModel<typeof serviceOffers>,
  'id'
>;
export type DbServiceOfferEquipment = InferSelectModel<
  typeof serviceOffersEquipments
>;
export type DbServiceOfferEquipmentInput = InferInsertModel<
  typeof serviceOffersEquipments
>;

// Technique types
export type DbTechnique = InferSelectModel<typeof techniques>;
export type DbTechniqueInput = Omit<InferInsertModel<typeof techniques>, 'id'>;
export type DbTechniqueI18N = InferSelectModel<typeof techniquesI18N>;
export type DbTechniqueI18NInput = InferInsertModel<typeof techniquesI18N>;

// Unit types
export type DbUnit = InferSelectModel<typeof units>;
export type DbUnitInput = Omit<InferInsertModel<typeof units>, 'id'>;
export type DbUnitI18N = InferSelectModel<typeof unitsI18N>;
export type DbUnitI18NInput = InferInsertModel<typeof unitsI18N>;
export type DbUnitType = InferSelectModel<typeof unitTypes>;
export type DbUnitTypeInput = Omit<InferInsertModel<typeof unitTypes>, 'id'>;
export type DbUnitTypeI18N = InferSelectModel<typeof unitTypesI18N>;
export type DbUnitTypeI18NInput = InferInsertModel<typeof unitTypesI18N>;
export type DbUnitParent = InferSelectModel<typeof unitParents>;
export type DbUnitParentInput = Omit<
  InferInsertModel<typeof unitParents>,
  'id'
>;
export type DbUnitAssociatedPerson = InferSelectModel<
  typeof unitAssociatedPeople
>;
export type DbUnitAssociatedPersonInput = InferInsertModel<
  typeof unitAssociatedPeople
>;

// Vendor types
export type DbVendor = InferSelectModel<typeof vendors>;
export type DbVendorInput = Omit<InferInsertModel<typeof vendors>, 'id'>;
export type DbVendorI18N = InferSelectModel<typeof vendorsI18N>;
export type DbVendorI18NInput = InferInsertModel<typeof vendorsI18N>;

// Visibility types
export type DbVisibility = InferSelectModel<typeof visibilities>;
export type DbVisibilityInput = Omit<
  InferInsertModel<typeof visibilities>,
  'id'
>;
export type DbVisibilityI18N = InferSelectModel<typeof visibilitiesI18N>;
export type DbVisibilityI18NInput = InferInsertModel<typeof visibilitiesI18N>;

// Add any missing association tables
export type DbRoomAssociatedCategory = InferSelectModel<
  typeof roomAssociatedCategories
>;
export type DbRoomAssociatedCategoryInput = InferInsertModel<
  typeof roomAssociatedCategories
>;

export type DbInfrastructureAssociatedInnovationLab = InferSelectModel<
  typeof infrastructureAssociatedInnovationLabs
>;
export type DbInfrastructureAssociatedInnovationLabInput = InferInsertModel<
  typeof infrastructureAssociatedInnovationLabs
>;

export type DbEquipmentAssociatedResearchField = InferSelectModel<
  typeof equipmentAssociatedResearchFields
>;
export type DbEquipmentAssociatedResearchFieldInput = InferInsertModel<
  typeof equipmentAssociatedResearchFields
>;

export type DbEquipmentAssociatedDocument = InferSelectModel<
  typeof equipmentAssociatedDocuments
>;
export type DbEquipmentAssociatedDocumentInput = InferInsertModel<
  typeof equipmentAssociatedDocuments
>;

// Export any enum types if needed
export type DbAddressType = 'campus' | 'civic';
