import { CampusesRepositoryLive } from '@/infrastructure/repositories/campuses.repository';
import {
    CreateCampusPayload,
    UpdateCampusPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class CampusesServiceLive extends Effect.Service<CampusesServiceLive>()(
    'CampusesServiceLive',
    {
        dependencies: [CampusesRepositoryLive.Default],
        effect: Effect.gen(function* () {
            const campusesRepository = yield* CampusesRepositoryLive;

            const getAllCampuses = () => {
                return Effect.gen(function* () {
                    return yield* campusesRepository.findAll();
                });
            };

            const getCampusById = (id: string) => {
                return Effect.gen(function* () {
                    const campus = yield* campusesRepository.findById(id);
                    if (!campus) {
                        return yield* Effect.fail(new Error(`Campus with id ${id} not found`));
                    }
                    return campus;
                });
            };

            const createCampus = (data: CreateCampusPayload) => {
                return Effect.gen(function* () {
                    return yield* campusesRepository.createWithTranslations({
                        campus: {
                            sadId: data.sadId,
                            institutionId: data.institutionId,
                            modifiedBy: data.modifiedBy,
                        },
                        translations: data.translations ? [...data.translations] : [],
                    });
                });
            };

            const updateCampus = (params: UpdateCampusPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingCampus = yield* campusesRepository.findById(id);
                    if (!existingCampus) {
                        return yield* Effect.fail(new Error(`Campus with id ${id} not found`));
                    }

                    return yield* campusesRepository.updateCampusWithTranslations({
                        campusId: id,
                        campus: {
                            sadId: updateData.sadId,
                            institutionId: updateData.institutionId,
                            modifiedBy: updateData.modifiedBy,
                        },
                        translations: updateData.translations ? [...updateData.translations] : [],
                    });
                });
            };

            const deleteCampus = (id: string) => {
                return Effect.gen(function* () {
                    const existingCampus = yield* campusesRepository.findById(id);
                    if (!existingCampus) {
                        return yield* Effect.fail(new Error(`Campus with id ${id} not found`));
                    }
                    return yield* campusesRepository.deleteOne(id);
                });
            };

            return {
                getAllCampuses,
                getCampusById,
                createCampus,
                updateCampus,
                deleteCampus,
            } as const;
        }),
    },
) { }