import { effectValidator } from '@hono/effect-validator';
import { Schemas } from '@rie/domain';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { handleEffectError } from '@/api/v2/utils/error-handler';
import { PermissionsRuntime } from '@/infrastructure/runtimes/permissions.runtime';
import { PermissionsServiceLive } from '@/infrastructure/services/permissions.service';
import {
  PermissionInputSchema,
  PermissionSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';

export const createPermissionRoute = describeRoute({
  description: 'Crée une permission',
  operationId: 'createPermission',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionInputSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PermissionSchema),
        },
      },
      description: 'Permission créée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission déjà existante',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const updatePermissionRoute = describeRoute({
  description: 'Mettre à jour une permission',
  operationId: 'updatePermission',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionInputSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PermissionSchema),
        },
      },
      description: 'Permission mise à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const deletePermissionRoute = describeRoute({
  description: 'Supprime une permission',
  operationId: 'deletePermission', // Fixed the operationId from updatePermission to deletePermission
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission supprimée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const getAllPermissionsRoute = describeRoute({
  description: 'Obtient toutes les permissions',
  operationId: 'getAllPermissions',
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(Schemas.ServiceOfferResponseSchema),
        },
      },
      description: 'Permissions retournées avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

const permissionsRoute = new Hono();

permissionsRoute.get('/', getAllPermissionsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const permissionsService = yield* PermissionsServiceLive;
    return yield* permissionsService.getAllPermissions();
  });

  const permissions = await PermissionsRuntime.runPromise(program);

  return ctx.json(permissions);
});

permissionsRoute.post(
  '/',
  createPermissionRoute,
  effectValidator('json', PermissionInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.createPermission(body);
    });

    const maybePermission = await PermissionsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybePermission);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybePermission)) {
      return ctx.json(maybePermission.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

permissionsRoute.put(
  '/:id',
  updatePermissionRoute,
  effectValidator('json', PermissionInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = await ctx.req.json();

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.updatePermission({ id, ...body });
    });

    const maybePermission = await PermissionsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybePermission);
    if (errorResponse) {
      return errorResponse;
    }

    return ctx.json({ error: 'Permission not found' }, 404);
  },
);

permissionsRoute.delete('/:id', deletePermissionRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const permissionsService = yield* PermissionsServiceLive;
    return yield* permissionsService.deletePermission(id);
  });

  const result = await PermissionsRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  return ctx.json({
    success: true,
    message: 'Permission deleted successfully',
  });
});

export default permissionsRoute;
