#!/bin/bash
set -e

# Navigate to the db-schema directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR" || exit 1

# Start the PostgreSQL container
echo "Starting PostgreSQL container..."
docker compose up -d

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until docker exec rie-database-postgres pg_isready -U ${PG_DATABASE_USER:-rie}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

echo "PostgreSQL is up and running!"

# Generate and run database migrations from db-schema
echo "Generating database migrations..."
pnpm db:generate
if [ $? -ne 0 ]; then
  echo "Database migration generation failed!"
  exit 1
fi

echo "Running database migrations..."
pnpm db:migrate
if [ $? -ne 0 ]; then
  echo "Database migration failed!"
  exit 1
fi

# Wait a moment to ensure migrations are fully applied
echo "Ensuring migrations are fully applied..."
sleep 2

# Execute the SQL file
echo "Executing SQL file..."
docker exec -i rie-database-postgres psql -U ${PG_DATABASE_USER:-rie} -d ${PG_DATABASE_NAME:-riedb} < src/migration-scripts/output/riedb-postgres_05-11-2025.sql

echo "Database setup complete!"
