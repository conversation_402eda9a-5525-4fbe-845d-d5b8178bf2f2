export interface Mapping {
  mysqlId: number | string;
  postgresId: string;
}

interface BaseI18N {
  locale: string;
}

export interface MySqlI18NBase extends BaseI18N {
  id: number;
  data_id: number;
  nom: string;
}

export interface PgI18NBase extends BaseI18N {
  id: string;
  data_id: string;
  name: string;
}

export interface PostgresI18NDescription extends PgI18NBase {
  description: string | null;
}

export interface PostgresI18nOtherNames extends PostgresI18NDescription {
  other_names: string | null;
}

export interface MySQLI18NDescription extends MySqlI18NBase {
  description: string | null;
}

export type MySqlI18NBaseReturn = Pick<MySqlI18NBase, 'id'>;

export type PostgresI18NBaseReturn = Pick<PgI18NBase, 'id'>;

export type MySQLBuilding = {
  id: number;
  sad_id: string;
  campus_id: number | null;
};

export type PostgresBuilding = {
  id: string;
  campus_id: string | null;
  civic_address_id: string | null;
  sad_id: string | null;
  di_id: string | null;
};
