volumes:
  postgres_data:
    driver: local

services:
  rie-postgres:
    image: postgres:latest
    container_name: rie-database-postgres
    hostname: rie-database.postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${PG_DATABASE_NAME:-riedb}
      POSTGRES_USER: ${PG_DATABASE_USER:-rie}
      POSTGRES_PASSWORD: ${PG_DATABASE_PASSWORD:-rie123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PG_DATABASE_USER:-rie}"]
      interval: 10s
      timeout: 5s
      retries: 5
