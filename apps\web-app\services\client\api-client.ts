import { env } from '@/env';
import { redirect } from '@/lib/navigation';
import { APIV2Error } from '@/services/api-v2-error';
import ky, {
  type AfterResponseHook,
  type BeforeErrorHook,
  type BeforeRequestHook,
  type KyInstance,
  type Options,
} from 'ky';
const isServer = typeof window === 'undefined';

const getLocaleFromCookies = async (): Promise<string> => {
  if (isServer) {
    try {
      const { cookies } = await import('next/headers');
      const cookiesStore = await cookies();
      const nextLocale = cookiesStore.get('NEXT_LOCALE');
      return nextLocale?.value ?? 'fr';
    } catch (e) {
      console.error('[API Client] Failed to access headers:', e);
      return 'fr'; // Default fallback
    }
  } else {
    try {
      const { getCookie } = await import('cookies-next');
      return getCookie('NEXT_LOCALE') ?? 'fr';
    } catch (e) {
      console.error('Failed to access cookies:', e);
      return 'fr'; // Default fallback
    }
  }
};

const getRieCookies = async () => {
  if (isServer) {
    try {
      // Dynamically import cookies only on server
      const { cookies } = await import('next/headers');
      const cookieStore = await cookies();

      // Get all cookies and format them as a proper cookie header
      return cookieStore
        .getAll()
        .filter((cookie) => cookie.name.startsWith('rie'))
        .map((cookie) => `${cookie.name}=${cookie.value}`)
        .join('; ');
    } catch (e) {
      console.error('Failed to access cookies:', e);
    }
  }
};

const beforeRequest: BeforeRequestHook[] = [
  async (request) => {
    const locale = await getLocaleFromCookies();
    if (isServer) {
      try {
        const { cookies } = await import('next/headers');
        const cookieStore = await cookies();

        const cookieHeader = cookieStore
          .getAll()
          .filter((cookie) => cookie.name.startsWith('rie'))
          .map((cookie) => `${cookie.name}=${cookie.value}`)
          .join('; ');

        if (cookieHeader) {
          request.headers.set('cookie', cookieHeader);
        } else {
          redirect({ href: { pathname: '/login' }, locale });
        }
      } catch (e) {
        console.error('Failed to access cookies:', e);
      }
    }
    return request;
  },
];

const beforeError: BeforeErrorHook[] = [
  async (error) => {
    const { response } = error;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    let errorData: any;
    let errorMessage = 'An error occurred';

    try {
      // Try to parse error response as JSON
      errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
    } catch (e) {
      // If JSON parsing fails, try to get text
      try {
        errorMessage = await response.text();
      } catch (textError) {
        // If text extraction fails, use status text
        errorMessage = response.statusText;
      }
    }

    return new APIV2Error(
      errorMessage,
      error.response,
      error.request,
      error.options,
      errorData,
    );
  },
];

const afterResponse: AfterResponseHook[] = [
  // Process successful responses
  async (_request, _options, response) => {
    const locale = await getLocaleFromCookies();
    if (response.status === 401) {
      redirect({ href: { pathname: '/login' }, locale });
    }

    return response;
  },
];

export const createApiClient = async () => {
  // Base configuration
  const locale = await getLocaleFromCookies();
  const config: Options = {
    prefixUrl: env.NEXT_PUBLIC_API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include' as const,
    hooks: {
      beforeRequest,
      beforeError,
      afterResponse,
    },
  };

  if (isServer) {
    try {
      const cookie = await getRieCookies();

      if (cookie) {
        config.headers = {
          ...config.headers,
          cookie,
        };
      } else {
        redirect({ href: { pathname: '/login' }, locale });
      }
    } catch (e) {
      console.error('Failed to access cookies:', e);
    }
  }

  return ky.extend(config);
};

// Singleton instance for client-side
let clientInstance: KyInstance | null = null;

// Get or create the API client
export const getApiClient = async () => {
  if (!isServer && clientInstance) {
    return clientInstance;
  }

  const client = await createApiClient();

  if (!isServer) {
    clientInstance = client;
  }

  return client;
};
