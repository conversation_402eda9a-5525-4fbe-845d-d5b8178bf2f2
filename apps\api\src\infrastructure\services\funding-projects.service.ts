import { FundingProjectsRepositoryLive } from '@/infrastructure/repositories/funding-projects.repository';
import {
    CreateFundingProjectPayload,
    UpdateFundingProjectPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class FundingProjectsServiceLive extends Effect.Service<FundingProjectsServiceLive>()(
    'FundingProjectsServiceLive',
    {
        dependencies: [FundingProjectsRepositoryLive],
        effect: Effect.gen(function* () {
            const fundingProjectsRepository = yield* FundingProjectsRepositoryLive;

            const getAllFundingProjects = () => {
                return Effect.gen(function* () {
                    return yield* fundingProjectsRepository.findAll();
                });
            };

            const getFundingProjectById = (id: string) => {
                return Effect.gen(function* () {
                    const project = yield* fundingProjectsRepository.findById(id);
                    if (!project) {
                        return yield* Effect.fail(new Error(`Funding project with id ${id} not found`));
                    }
                    return project;
                });
            };

            const createFundingProject = (data: CreateFundingProjectPayload) => {
                return Effect.gen(function* () {
                    return yield* fundingProjectsRepository.createWithTranslations({
                        project: {
                            startDate: data.startDate,
                            endDate: data.endDate,
                            modifiedBy: data.modifiedBy,
                        },
                        translations: data.translations ? [...data.translations] : [],
                    });
                });
            };

            const updateFundingProject = (params: UpdateFundingProjectPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingProject = yield* fundingProjectsRepository.findById(id);
                    if (!existingProject) {
                        return yield* Effect.fail(new Error(`Funding project with id ${id} not found`));
                    }

                    return yield* fundingProjectsRepository.updateOne({
                        id,
                        startDate: updateData.startDate,
                        endDate: updateData.endDate,
                        modifiedBy: updateData.modifiedBy,
                    });
                });
            };

            const deleteFundingProject = (id: string) => {
                return Effect.gen(function* () {
                    const existingProject = yield* fundingProjectsRepository.findById(id);
                    if (!existingProject) {
                        return yield* Effect.fail(new Error(`Funding project with id ${id} not found`));
                    }
                    return yield* fundingProjectsRepository.deleteOne(id);
                });
            };

            return {
                getAllFundingProjects,
                getFundingProjectById,
                createFundingProject,
                updateFundingProject,
                deleteFundingProject,
            } as const;
        }),
    },
) { }