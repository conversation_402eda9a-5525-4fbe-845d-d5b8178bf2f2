import {
  BuildingNotFoundError,
  PermissionAlreadyExistsError,
  PermissionGroupAlreadyExistsError,
  PermissionGroupNotFoundError,
  PermissionNotFoundError,
  RoleAlreadyExistsError,
  RoleNotFoundError,
} from '@rie/domain/errors';
import { Database } from '@rie/postgres-db';
import * as Cause from 'effect/Cause';
import * as Exit from 'effect/Exit';
import type { Context } from 'hono';

export const handleEffectError = <T>(
  ctx: Context,
  exit: Exit.Exit<T, unknown>,
): Response | null => {
  if (Exit.isSuccess(exit)) {
    return null;
  }

  const squashed = Cause.squash(exit.cause);

  // Handle domain-specific errors
  if (squashed instanceof BuildingNotFoundError) {
    return ctx.json(
      { error: `Building with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionNotFoundError) {
    return ctx.json(
      { error: `Permission with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionAlreadyExistsError) {
    return ctx.json(
      {
        error: `Permission ${squashed.domain}:${squashed.action} already exists`,
      },
      409,
    );
  }

  if (squashed instanceof PermissionGroupNotFoundError) {
    return ctx.json(
      { error: `Permission group with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionGroupAlreadyExistsError) {
    return ctx.json(
      { error: `Permission group ${squashed.name} already exists` },
      409,
    );
  }

  if (squashed instanceof RoleNotFoundError) {
    return ctx.json({ error: `Role with id ${squashed.id} not found` }, 404);
  }

  if (squashed instanceof RoleAlreadyExistsError) {
    return ctx.json({ error: `Role ${squashed.name} already exists` }, 409);
  }

  // Handle database errors
  if (squashed instanceof Database.DatabaseError) {
    return ctx.json({ error: squashed.message }, 400);
  }

  // Handle generic errors
  return ctx.json({ error: 'An unexpected error occurred' }, 500);
};
