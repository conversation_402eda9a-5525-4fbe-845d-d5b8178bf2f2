import * as Schema from 'effect/Schema';
import { descriptionSchema } from './base.schema';

// — Translation shape
export const InstitutionTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(descriptionSchema),
  otherNames: Schema.optional(Schema.String),
  acronyms: Schema.optional(Schema.String),
});

export type InstitutionTranslation = Schema.Schema.Type<typeof InstitutionTranslationSchema>;

// — Full Institution shape (corresponds to institutions in DB)
export const InstitutionSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  guidId: Schema.optional(Schema.String),       // nullable
  typeId: Schema.String,                        // required
  translations: Schema.Array(InstitutionTranslationSchema), // at least []
  createdAt: Schema.String,                     // ISO timestamp
  updatedAt: Schema.String,                     // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),  // nullable
});

export type Institution = Schema.Schema.Type<typeof InstitutionSchema>;

// — Input schemas for API
export const CreateInstitutionSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.String,
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  ),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateInstitutionPayload = Schema.Schema.Type<typeof CreateInstitutionSchema>;

export const UpdateInstitutionSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  )),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateInstitutionPayload = Schema.Schema.Type<typeof UpdateInstitutionSchema> & { id: string };

// — Response schemas
export const InstitutionResponseSchema = InstitutionSchema;
export const InstitutionListResponseSchema = Schema.Array(InstitutionResponseSchema);

export type InstitutionResponse = Schema.Schema.Type<typeof InstitutionResponseSchema>;
export type InstitutionListResponse = Schema.Schema.Type<typeof InstitutionListResponseSchema>;
