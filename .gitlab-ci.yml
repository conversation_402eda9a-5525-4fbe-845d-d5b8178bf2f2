stages:
  - debug
  - tests-e2e
  - build-commit
  - build-image
  - create-tag
  - deploy
  - stage
  - mr-tests
  # - tests

# workflow:
#   rules:
#     # For detached merge requests, run the pipeline to validate before merging
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
#       when: always
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_EVENT_TYPE == "detached" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH'
#       when: always
#     # # For maintainer-approved merge requests, run the pipeline to validate before merging
#     # - if: $CI_MERGE_REQUEST_APPROVED && $CI_PIPELINE_SOURCE == "merge_request_event"
#     #   when: always
# #     # For regular branch pushes, run the default pipeline
# #     - if: $CI_COMMIT_BRANCH
# #       when: always
# #     # For tags
# #     - if: $CI_COMMIT_TAG
# #       when: always

variables:
  DOCKER_PLATFORM_IMAGE: "linux/amd64"
  NEXT_PUBLIC_BASE_PATH: /rie/public
  RIE_DEV__HOST_DOMAIN: devel-cen.cen.umontreal.ca
  SLEEP_TIME: 60
  # Needed for merge request automation
  # This token must be set up as a CI/CD variable in the GitLab project settings
  # with sufficient permissions to merge merge requests
  # Create a Personal Access Token with api scope at User Settings > Access Tokens
  GITLAB_API_TOKEN: ${PROJECT_ACCESS_TOKEN}
  # NEXT_PUBLIC_BASE_PATH: /public
  # RIE_DEV__HOST_DOMAIN: rie-devel.cen.umontreal.ca
  TEST_RESULT: 0

debug-pipeline-source:
  stage: debug
  tags:
    - devel-cen
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
  script:
    - echo "CI_PIPELINE_SOURCE=$CI_PIPELINE_SOURCE"
    - echo "CI_MERGE_REQUEST_EVENT_TYPE=$CI_MERGE_REQUEST_EVENT_TYPE"
    - env | grep -E '^(CI_|GITLAB_)'

build-commit-image:
  stage: build-commit
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    # - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
    # - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+-?.*$/
    - if: $CI_COMMIT_BRANCH =~ /^(dev|test|fix|prod|devops|ci|debug)-.*$/
    # run on detached merge requests and "pipelines must succeed" is enabled
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
    - if: $CI_COMMIT_BRANCH =~ /^(devops)$/
    - if: $CI_COMMIT_BRANCH =~ /^(fix-auth-cookies)$/
  tags:
    - devel-cen
  script:
    - echo "CI_MERGE_REQUEST_EVENT_TYPE=$CI_MERGE_REQUEST_EVENT_TYPE"
    - echo "CI_PIPELINE_SOURCE=$CI_PIPELINE_SOURCE"
    - docker build . -t $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}
      -f apps/web-app/Dockerfile
      --build-arg=HTTP_PROXY=$INTERNAL_HTTP_PROXY
      --build-arg=HTTPS_PROXY=$INTERNAL_HTTP_PROXY
      --build-arg=NO_PROXY="$NO_PROXY"
      --build-arg=NEXT_ENABLE_DEBUG=$NEXT_ENABLE_DEBUG
    - docker push $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}

## Build latest image
build-latest-image:
  stage: build-image
  # needs: ["build-commit-image"]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
  tags:
    - devel-cen
  script:
    - docker pull $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}
    - docker tag $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA} $DOCKER_REGISTRY/${CI_PROJECT_PATH}:latest
    - docker push $DOCKER_REGISTRY/${CI_PROJECT_PATH}:latest

build-image:
  stage: build-image
  # needs: ["build-commit-image"]
  rules:
    # - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
    - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+-?.*$/
    - if: $CI_COMMIT_BRANCH =~ /^(devops)$/
    - if: $CI_COMMIT_BRANCH =~ /^(fix-auth-cookies)$/
    - if: $CI_COMMIT_BRANCH =~ /^(dev|test|fix|prod|devops|ci|debug)-.*$/
  tags:
    - devel-cen
  script:
    - docker pull $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}
    - docker tag $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA} $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}
    - docker push $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}

## Create a new tag
create-day-tag:
  stage: create-tag
  # needs: ["run-tests-e2e"]
  tags:
    - devel-cen
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    # - if: $CI_COMMIT_BRANCH =~ /^ci-.*$/
    # when: always
  script:
    - export TAG="v$(date +'%Y.%m.%d')"
    - echo "Creating tag $TAG"
    - git config --global user.name "Project Bot"
    - git config --global user.email "project-bot@${CI_SERVER_HOST}"
    - git remote set-url origin "https://project-bot:${PROJECT_ACCESS_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git tag -d $TAG || true
    - git push origin :refs/tags/$TAG || true
    - git tag $TAG
    - git push origin $TAG
    - echo "Tag $TAG created and pushed successfully"

## Run tests
run-tests-e2e:
  stage: tests-e2e
  # needs: ["build-commit-image"]
  # allow_failure: true
  tags:
    - devel-cen
    - docker
  rules:
    # - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_EVENT_TYPE == "detached" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH'
    - if: $CI_COMMIT_BRANCH =~ /^ci-.*$/
    # - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
    #   when: manual
    # - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+.?.*$/
    #   when: manual
  script:
    - echo "Running e2e tests for $CI_COMMIT_SHORT_SHA"
    - if [ ! -d "tests-${CI_COMMIT_SHORT_SHA}" ]; then mv tests tests-${CI_COMMIT_SHORT_SHA}; fi
    - sed -i -e "s|test--|test-${CI_COMMIT_SHORT_SHA}--|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - sed -i -e "s|test/rie/rie-web-app:latest|$DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - sed -i -e "s|test/rie/rie-web-app-e2e:latest|test/rie/rie-web-app-e2e:${CI_COMMIT_SHORT_SHA}|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - cat tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml build $USE_CACHE
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml down
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml up -d $DOCKER_COMPOSE_UP_OPTIONS
    - sleep $SLEEP_TIME
    - docker exec test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e make list-tests
    - docker exec test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e make run-tests || TEST_RESULT=1
    - docker cp test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e:/app/apps/web-app-e2e/playwright-report tests-${CI_COMMIT_SHORT_SHA}/playwright-report/
    # - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml down
    - |
      if [ "$TEST_RESULT" -ne 0 ]; then
        echo "Tests failed. Merge request will not be completed."
        exit 1
      else
        echo "Tests passed. Merge request can be completed."
      fi
  artifacts:
    when: always
    paths:
      - tests-${CI_COMMIT_SHORT_SHA}/playwright-report/
    expire_in: 30 days
  after_script:
    - |
      if [ $CI_JOB_STATUS == "success" ]; then
        echo "Pipeline Status: ✅ E2E Tests Passed"
      else
        echo "Pipeline Status: ❌ E2E Tests Failed"
      fi

## Run commit tests
run-tests:
  # stage: tests
  stage: deploy
  # needs: ["deploy-web-app-on-devel-cen"]
  allow_failure: true
  tags:
    - devel-cen
    - docker
  rules:
    # - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    #   when: manual
    # - if: $CI_COMMIT_BRANCH =~ /^devops-?.*$/
    #   when: manual
    - if: $CI_COMMIT_BRANCH =~ /^ci-.*$/
      when: manual
    # - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
    # - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+.?.*$/
  script:
    - echo "Running tests for $CI_COMMIT_SHORT_SHA"
    - if [ ! -d "tests-${CI_COMMIT_SHORT_SHA}" ]; then mv tests tests-${CI_COMMIT_SHORT_SHA}; fi
    - sed -i -e "s|test--|test-${CI_COMMIT_SHORT_SHA}--|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - sed -i -e "s|test/rie/rie-web-app:latest|$DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_SHORT_SHA}|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - sed -i -e "s|test/rie/rie-web-app-e2e:latest|test/rie/rie-web-app-e2e:${CI_COMMIT_SHORT_SHA}|" tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - cat tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml build $USE_CACHE
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml down
    - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml up -d $DOCKER_COMPOSE_UP_OPTIONS
    - sleep $SLEEP_TIME
    - docker exec test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e make list-tests
    - docker exec test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e pnpm playwright test || TEST_RESULT=1
    - docker cp test-${CI_COMMIT_SHORT_SHA}--rie-web-app-e2e:/app/apps/web-app-e2e/playwright-report tests-${CI_COMMIT_SHORT_SHA}/playwright-report/
    # - docker compose -f tests-${CI_COMMIT_SHORT_SHA}/docker-compose.yml down
    - |
      if [ "$TEST_RESULT" -ne 0 ]; then
        echo "Tests failed. Merge request will not be completed."
        exit 1
      else
        echo "Tests passed. Merge request can be completed."
      fi
  artifacts:
    when: always
    paths:
      - tests-${CI_COMMIT_SHORT_SHA}/playwright-report/
    expire_in: 30 days
  after_script:
    - |
      if [ $CI_JOB_STATUS == "success" ]; then
        echo "Pipeline Status: ✅ E2E Tests Passed"
      else
        echo "Pipeline Status: ❌ E2E Tests Failed"
      fi
# build-web-app-latest-e2e:
#   stage: build
#   rules:
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
#       when: always
#   tags:
#     - devel-cen
#   script:
#     - docker build . -t $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"latest"}-e2e
#       -f apps/web-app-e2e/Dockerfile
#       --build-arg=HTTP_PROXY=$INTERNAL_HTTP_PROXY
#       --build-arg=HTTPS_PROXY=$INTERNAL_HTTP_PROXY
#       --build-arg=NO_PROXY="$NO_PROXY"
#     - docker push $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"latest"}-e2e

# build-web-app-branches-e2e:
#   stage: build
#   rules:
#     - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
#       when: always
#     - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+-?.*$/
#       when: always
#     - if: $CI_COMMIT_BRANCH =~ /^devops-?.*$/
#       when: always
#     - if: $CI_COMMIT_BRANCH =~ /^(dev|test|fix|prod)-.*$/
#       when: always
#   tags:
#     - devel-cen
#   script:
#     - docker build . -t $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}-e2e
#       -f apps/web-app-e2e/Dockerfile
#       --build-arg=HTTP_PROXY=$INTERNAL_HTTP_PROXY
#       --build-arg=HTTPS_PROXY=$INTERNAL_HTTP_PROXY
#       --build-arg=NO_PROXY="$NO_PROXY"
#     - docker push $DOCKER_REGISTRY/${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}-e2e

deploy-devel-cen:
  stage: deploy
  # needs: ["create-day-tag"]
  tags:
    - registry
  rules:
    # - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    #   when: manual
    - if: $CI_COMMIT_BRANCH =~ /^devops-?.*$/
      when: manual
    - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+.?.*$/
    - if: $CI_COMMIT_TAG =~ /^v\d+-\d+-\d+.?.*$/
    - if: $CI_COMMIT_BRANCH =~ /^(dev|test|fix|prod|devops|ci|debug)-.*$/
      when: manual
    - if: $CI_COMMIT_BRANCH =~ /^(fix-auth-cookies)$/
  script:
    - docker pull $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_BRANCH"}
    - docker rm -f ${CI_PROJECT_NAME}-public
    - docker run -dit $RIE_DOCKER_RUN_CMD
      --name ${CI_PROJECT_NAME}-public
      --hostname ${CI_PROJECT_NAME}-public
      --network devel-cen.net
      --add-host=$FQDN_PROXY
      -e APP_ENV=prod
      -e NEXT_PUBLIC_BASE_PATH=/rie/public
      -e NEXT_PUBLIC_ORIGIN_URL=https://${RIE_DEV__HOST_DOMAIN}
      -e NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY:-"fakeKey"}
      -e NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST:-"https://us.i.posthog.com"}
      -e NEXT_PUBLIC_RIE_API_URL=${RIE_DEV__NEXT_PUBLIC_RIE_API_URL:-"https://rie-devel.cen.umontreal.ca/api/"}
      -e NEXT_PUBLIC_RIE_AUTH_URL=${RIE_DEV__NEXT_PUBLIC_RIE_AUTH_URL:-"https://rie-devel.cen.umontreal.ca/api/auth"}
      -e RIE_AUTH_CLIENT_ID=${RIE_DEV__OIDC_CLIENT_ID:-"testclient"}
      -e RIE_AUTH_CLIENT_SECRET=${RIE_DEV__OIDC_CLIENT_SECRET:-"testpass"}
      -e NODE_ENV=${NODE_ENV:-"production"}
      -e NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-"https://rie-devel.cen.umontreal.ca:4000/api"}
      -e PG_DATABASE_URL=${PG_DATABASE_URL:-"postgres://postgres:postgres@localhost:5432/postgres"}
      -e BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET:-"BETTER_AUTH_SECRET"}
      -e GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID:-"********************"}
      -e GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET:-"2f690ab9393b1287d38feb2dce23234118b4a2b4"}
      -e MICROSOFT_CLIENT_ID=${MICROSOFT_CLIENT_ID:-"a93219e8-f9a3-4221-85d6-32b06ee72251"}
      -e MICROSOFT_CLIENT_SECRET=${MICROSOFT_CLIENT_SECRET:-"****************************************"}
      --label traefik.enable=true
      --label traefik.http.services.${CI_PROJECT_NAME}-public.loadbalancer.server.port=3000
      --label traefik.http.routers.${CI_PROJECT_NAME}-public-web.entrypoints=web
      --label traefik.http.routers.${CI_PROJECT_NAME}-public-web.rule="Host(\`${RIE_DEV__HOST_DOMAIN}\`)&&(PathPrefix(\`/rie/public\`))"
      --label traefik.http.routers.${CI_PROJECT_NAME}-web.service=${CI_PROJECT_NAME}-public
      --label traefik.http.routers.${CI_PROJECT_NAME}-public.entrypoints=websecure
      --label traefik.http.routers.${CI_PROJECT_NAME}-public.rule="Host(\`${RIE_DEV__HOST_DOMAIN}\`)&&(PathPrefix(\`/rie/public\`))"
      --label traefik.http.routers.${CI_PROJECT_NAME}-public.service=${CI_PROJECT_NAME}-public
      --label traefik.http.routers.${CI_PROJECT_NAME}-public.tls=true
      $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_BRANCH"}
    - echo "Cleanup completed"
    - echo "deployed at http://$HOST_DOMAIN/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}  https://$HOST_DOMAIN/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}"

# deploy-devel:
#   stage: deploy
#   tags:
#     - registry
#   rules:
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
#       when: always
#     - if: $CI_COMMIT_BRANCH =~ /^devops-?.*$/
#       when: manual
#     - if: $CI_COMMIT_TAG =~ /^v\d+.\d+.\d+-?.*$/
#       when: manual
#     # - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
#     #   when: manual
#   script:
#     - docker pull $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_BRANCH"}
#     - docker rm -f ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}
#     - docker run -dit $RIE_DOCKER_RUN_CMD
#       --name ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}
#       --hostname ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}
#       --network devel-cen.net
#       --add-host=$FQDN_PROXY
#       -e APP_ENV=prod
#       -e NEXT_PUBLIC_BASE_PATH=/rie/${CI_COMMIT_BRANCH:-"public"}
#       -e NEXT_PUBLIC_ORIGIN_URL=https://${RIE_DEV__HOST_DOMAIN}
#       -e NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY:-"fakeKey"}
#       -e NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST:-"https://us.i.posthog.com"}
#       -e NEXT_PUBLIC_RIE_API_URL=${RIE_DEV__NEXT_PUBLIC_RIE_API_URL:-"https://rie-devel.cen.umontreal.ca/api/"}
#       -e NEXT_PUBLIC_RIE_AUTH_URL=${RIE_DEV__NEXT_PUBLIC_RIE_AUTH_URL:-"https://rie-devel.cen.umontreal.ca/api/auth"}
#       -e RIE_AUTH_CLIENT_ID=${RIE_DEV__OIDC_CLIENT_ID:-"testclient"}
#       -e RIE_AUTH_CLIENT_SECRET=${RIE_DEV__OIDC_CLIENT_SECRET:-"testpass"}
#       --label traefik.enable=true
#       --label traefik.http.services.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}.loadbalancer.server.port=3000
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}-web.entrypoints=web
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}-web.rule="Host(\`${RIE_DEV__HOST_DOMAIN}\`)&&(PathPrefix(\`/rie/${CI_COMMIT_BRANCH:-"public"}\`))"
#       --label traefik.http.routers.${CI_PROJECT_NAME}-web.service=${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}.entrypoints=websecure
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}.rule="Host(\`${RIE_DEV__HOST_DOMAIN}\`)&&(PathPrefix(\`/rie/${CI_COMMIT_BRANCH:-"public"}\`))"
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}.service=${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}
#       --label traefik.http.routers.${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH:-"public"}.tls=true
#       $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_BRANCH"}
#     -
#     - echo "deployed at http://${RIE_DEV__HOST_DOMAIN}/rie/${CI_COMMIT_BRANCH:-"public"}  https://${RIE_DEV__HOST_DOMAIN}/rie/${CI_COMMIT_BRANCH:-"public"}"

# build-prod:
#   stage: build
#   only: [main, tags]
#   tags: [atelier1-cen, shell]
#   script:
#     - docker build . -t $DOCKER_REGISTRY/prod-${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"latest"}
#       --build-arg=NEXT_PUBLIC_BASE_PATH=${RIE_PROD__BASE_PATH:-/donnees}
#       --build-arg=NEXT_PUBLIC_ORIGIN_URL=${RIE_PROD__NEXT_PUBLIC_ORIGIN_URL:-"https://rie.umontreal.ca"}
#       --build-arg=NEXT_PUBLIC_RIE_API_URL=${RIE_PROD__NEXT_PUBLIC_RIE_API_URL:-"https://api.rie.umontreal.ca/"}
#       --build-arg=NEXT_PUBLIC_RIE_AUTH_URL=${RIE_PROD__NEXT_PUBLIC_RIE_AUTH_URL:-"https://api.rie.umontreal.ca/auth"}
#       --build-arg=RIE_AUTH_CLIENT_ID=${RIE_PROD__OIDC_CLIENT_ID:-"testclient"}
#       --build-arg=RIE_AUTH_CLIENT_SECRET=${RIE_PROD__OIDC_CLIENT_SECRET:-"testpass"}
#     - docker push $DOCKER_REGISTRY/prod-${CI_PROJECT_PATH}:${CI_COMMIT_TAG:-"latest"}

# deploy-prod-cen:
#   stage: deploy
#   # needs: ["deploy-web-app-on-devel-cen"]
#   only: [tags]
#   tags: [devel01-cen]
#   when: manual
#   script:
#     - rie-frontend deploy ${CI_COMMIT_TAG:-"latest"}

# build-branch:
#   stage: build
#   # only:
#   #   - dev-container
#   #   # - dev-*
#   # when: manual
#   rules:
#     - if: $CI_COMMIT_BRANCH =~ /^dev-.*$/
#       when: always
#   tags: [atelier1-cen, shell]
#   script:
#     # - docker pull docker.io/cenr/nodejs:latest
#     - docker build . -t $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}
#       --build-arg=NEXT_PUBLIC_BASE_PATH=${NEXT_PUBLIC_BASE_PATH}
#     - docker push $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}
#     # - docker buildx build . --push -t $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_REF_NAME"}
#     #   --platform="linux/amd64"

mr-builder:
  stage: build-image
  only:
    - cicd
  # rules:
  #   - if: $CI_COMMIT_TAG =~ /^cicd.*$/
  #     when: always
  tags: [atelier2-cen, dind]
  script:
    - docker login -u $DOCKER_REGISTRY_USER -p $DOCKER_REGISTRY_PASSWORD $DOCKER_REGISTRY
    # - echo $CI_PROJECT_PATH
    - docker build . -t $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
      -f apps/web-app/Dockerfile
      --build-arg=NEXT_PUBLIC_BASE_PATH=/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
      --build-arg=NEXT_PUBLIC_ORIGIN_URL=https://$HOST_DOMAIN
    - docker push $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}

mr-deploy-devel-cen:
  stage: deploy
  # needs: ["mr-builder"]
  tags:
    - registry
  only:
    - cicd
  # rules:
  #   - if: $CI_COMMIT_TAG =~ /^cicd.*$/
  #     when: always
  script:
    - docker pull $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
    - docker rm -f ${CI_PROJECT_NAME}-${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
    - docker run -dit $RIE_DOCKER_RUN_CMD
      --name ${CI_PROJECT_NAME}-${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
      --hostname ${CI_PROJECT_NAME}-${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
      --network devel-cen.net
      --add-host=$FQDN_PROXY
      -e APP_ENV=prod
      --label traefik.enable=true
      --label traefik.http.services.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}).loadbalancer.server.port=3000
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"})-web.entrypoints=web
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"})-web.rule="Host(\`$HOST_DOMAIN\`)&&(PathPrefix(\`/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}\`))"
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"})-web.service=${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"})
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}).entrypoints=websecure
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}).rule="Host(\`$HOST_DOMAIN\`)&&(PathPrefix(\`/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}\`))"
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}).service=${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"})
      --label traefik.http.routers.${CI_PROJECT_NAME}-$(sed -e 's|\.|-|g;s|/|-|g' <<< ${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}).tls=true
      $DOCKER_REGISTRY/$CI_PROJECT_PATH:${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}
    - echo "Cleanup completed"
    - echo "deployed at http://$HOST_DOMAIN/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}  https://$HOST_DOMAIN/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}"
    - docker exec rie-apiserver.devel-cen make update-testclient-redirect-uri uri=https://$HOST_DOMAIN/${CI_PROJECT_PATH}/${CI_COMMIT_TAG:-"$CI_COMMIT_SHORT_SHA"}/api/login/umontreal/callback
