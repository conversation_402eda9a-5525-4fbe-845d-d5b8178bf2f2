import { handleEffectError } from '@/api/v2/utils/error-handler';
import { ControlledListsRuntime } from '@/infrastructure/runtimes/controlled-lists.runtime';
import { ControlledListsServiceLive } from '@/infrastructure/services/controlled-lists.service';
import { effectValidator } from '@hono/effect-validator';
import {
  ControlledListsQuerySchema,
  ControlledListsResponseSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Controlled Lists Route
export const getControlledListsRoute = describeRoute({
  description: 'Obtient des listes contrôlées pour plusieurs entités',
  operationId: 'getControlledLists',
  parameters: [
    {
      name: 'entities',
      in: 'query',
      required: true,
      schema: resolver(Schema.Array(Schema.String)),
      description:
        'Liste des entités à récupérer (ex: equipmentStatuses,equipmentCategories)',
      explode: false,
      style: 'form',
    },
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(
        Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
      ),
      description: 'Type de vue (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(
        Schema.Union(Schema.Literal('fr'), Schema.Literal('en')),
      ),
      description: 'Locale pour les traductions',
    },
    {
      name: 'limit',
      in: 'query',
      required: false,
      schema: resolver(Schema.NumberFromString),
      description: "Limite du nombre d'éléments (-1 pour tous)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(ControlledListsResponseSchema),
        },
      },
      description: 'Listes contrôlées retournées avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Paramètres non valides',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Listes contrôlées'],
});

// Create the router
const controlledListsRoute = new Hono();

// Get controlled lists
controlledListsRoute.get(
  '/',
  getControlledListsRoute,
  effectValidator('query', ControlledListsQuerySchema),
  async (ctx) => {
    const { entities, view, locale, limit } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const controlledListsService = yield* ControlledListsServiceLive;
      return yield* controlledListsService.getControlledLists({
        entities,
        view,
        limit,
        locale,
      });
    });

    const result = await ControlledListsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { controlledListsRoute };
