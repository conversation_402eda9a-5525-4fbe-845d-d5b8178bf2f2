import * as fs from 'node:fs/promises';
import { BaseConverter } from '@/migration-scripts/base-converter';
import { DbUtils } from '@rie/utils';

export class PermissionsMigrationConverter extends BaseConverter {
  private permissionIdMappings: Record<string, string> = {};
  async convertFile(outputPath: string): Promise<void> {
    // Define permission domains
    const domains = [
      'equipment',
      'infrastructure',
      'unit',
      'institution',
      'vendor',
      'room',
      'building',
      'campus',
      'fundingProject',
    ];

    // Define common actions for each domain
    const commonActions = ['read', 'create', 'update', 'delete'];
    // The SQL content to append
    let permissionsInsertStatement =
      'INSERT INTO permissions (id, domain, action) VALUES\n';
    // Generate domain-specific permissions
    domains.forEach((domain, domainIndex) => {
      commonActions.forEach((action, actionIndex) => {
        const permissionId = DbUtils.cuid2();
        permissionsInsertStatement += `('${permissionId}', '${domain}', '${action}')${domainIndex === domains.length - 1 && actionIndex === commonActions.length - 1 ? ';' : ',\n'}`;
        this.permissionIdMappings[`${domain}-${action}`] = permissionId;
      });
    });

    // Create the output directory if it doesn't exist
    const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
    await fs.mkdir(outputDir, { recursive: true });

    await this.writeMappingsToJson(
      outputDir,
      { mysql: 'permissions', postgres: 'permissions' },
      this.permissionIdMappings,
    );
    // Append the SQL content to the output file
    await fs.appendFile(outputPath, permissionsInsertStatement);
    console.log('Permissions data added successfully!');
  }
}
