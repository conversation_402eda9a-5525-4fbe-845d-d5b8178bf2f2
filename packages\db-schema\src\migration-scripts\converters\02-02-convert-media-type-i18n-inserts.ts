import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class MediaTypeI18nMigrationConverter extends BaseConverter {
  private mediaTypeI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    mediaTypeIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the media_type
    const newMediaTypeId = mediaTypeIdMappings[mysqlRecord.data_id.toString()];
    if (!newMediaTypeId) {
      throw new Error(
        `No mapping found for media_type_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.mediaTypeI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newMediaTypeId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for media_type_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'media_type_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No media_type_trad INSERT statements found.');
        return;
      }

      // Load media_type ID mappings
      const mediaTypeIdMappings = await this.loadEntityIdMappings('media_type');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, mediaTypeIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'media_types_i18n',
        'Media Type I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.mediaTypeI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'media_type_trad', postgres: 'media_types_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} media_types_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
