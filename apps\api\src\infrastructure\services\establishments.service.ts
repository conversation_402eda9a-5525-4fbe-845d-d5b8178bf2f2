import { EstablishmentsRepositoryLive } from '@/infrastructure/repositories/establishments.repository';
import {
    CreateEstablishmentPayload,
    UpdateEstablishmentPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class EstablishmentsServiceLive extends Effect.Service<EstablishmentsServiceLive>()(
    'EstablishmentsServiceLive',
    {
        dependencies: [EstablishmentsRepositoryLive],
        effect: Effect.gen(function* () {
            const establishmentsRepository = yield* EstablishmentsRepositoryLive;

            const getAllEstablishments = () => {
                return Effect.gen(function* () {
                    return yield* establishmentsRepository.findAll();
                });
            };

            const getEstablishmentById = (id: string) => {
                return Effect.gen(function* () {
                    const establishment = yield* establishmentsRepository.findById(id);
                    if (!establishment) {
                        return yield* Effect.fail(new Error(`Establishment with id ${id} not found`));
                    }
                    return establishment;
                });
            };

            const createEstablishment = (data: CreateEstablishmentPayload) => {
                return Effect.gen(function* () {
                    return yield* establishmentsRepository.createWithTranslations({
                        establishment: {
                            guidId: data.guidId,
                            typeId: data.typeId,
                            modifiedBy: data.modifiedBy,
                        },
                        translations: data.translations ? [...data.translations] : [],
                    });
                });
            };

            const updateEstablishment = (params: UpdateEstablishmentPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingEstablishment = yield* establishmentsRepository.findById(id);
                    if (!existingEstablishment) {
                        return yield* Effect.fail(new Error(`Establishment with id ${id} not found`));
                    }

                    return yield* establishmentsRepository.updateOne({
                        id,
                        guidId: updateData.guidId,
                        typeId: updateData.typeId,
                        modifiedBy: updateData.modifiedBy,
                    });
                });
            };

            const deleteEstablishment = (id: string) => {
                return Effect.gen(function* () {
                    const existingEstablishment = yield* establishmentsRepository.findById(id);
                    if (!existingEstablishment) {
                        return yield* Effect.fail(new Error(`Establishment with id ${id} not found`));
                    }
                    return yield* establishmentsRepository.deleteOne(id);
                });
            };

            return {
                getAllEstablishments,
                getEstablishmentById,
                createEstablishment,
                updateEstablishment,
                deleteEstablishment,
            } as const;
        }),
    },
) { }