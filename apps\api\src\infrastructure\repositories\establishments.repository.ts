import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
    ConfigLive.pipe(
        Effect.map((env) =>
            Database.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
        ),
    ),
).pipe(Layer.provide(ConfigLive.Default));

export class EstablishmentsRepositoryLive extends Effect.Service<EstablishmentsRepositoryLive>()(
    'EstablishmentsRepositoryLive',
    {
        dependencies: [PgDatabaseLive],
        effect: Effect.gen(function* ($) {
            const db = yield* $(Database.PgDatabase);

            const findAll = db.makeQuery((exec) =>
                exec((client) =>
                    client.query.institutions.findMany({
                        columns: {
                            id: true,
                            guidId: true,
                            typeId: true,
                            createdAt: true,
                            updatedAt: true,
                            modifiedBy: true,
                        },
                        with: {
                            translations: {
                                columns: {
                                    id: true,
                                    locale: true,
                                    name: true,
                                    description: true,
                                    otherNames: true,
                                    acronyms: true,
                                },
                            },
                        },
                    }),
                ),
            );

            const findById = db.makeQuery((exec, id: string) =>
                exec((client) =>
                    client.query.institutions.findFirst({
                        where: eq(DBSchema.institutions.id, id),
                        columns: {
                            id: true,
                            guidId: true,
                            typeId: true,
                            createdAt: true,
                            updatedAt: true,
                            modifiedBy: true,
                        },
                        with: {
                            translations: {
                                columns: {
                                    id: true,
                                    locale: true,
                                    name: true,
                                    description: true,
                                    otherNames: true,
                                    acronyms: true,
                                },
                            },
                        },
                    }),
                ),
            );

            const createOne = db.makeQuery(
                (
                    exec,
                    params: {
                        guidId?: string | null;
                        typeId: string;
                        modifiedBy?: string | null;
                    },
                ) =>
                    exec((client) =>
                        client
                            .insert(DBSchema.institutions)
                            .values({
                                guidId: params.guidId,
                                typeId: params.typeId,
                                modifiedBy: params.modifiedBy,
                            })
                            .returning({
                                id: DBSchema.institutions.id,
                                guidId: DBSchema.institutions.guidId,
                                typeId: DBSchema.institutions.typeId,
                                createdAt: DBSchema.institutions.createdAt,
                                updatedAt: DBSchema.institutions.updatedAt,
                                modifiedBy: DBSchema.institutions.modifiedBy,
                            }),
                    ),
            );

            const updateOne = db.makeQuery(
                (
                    exec,
                    params: {
                        id: string;
                        guidId?: string | null;
                        typeId?: string;
                        modifiedBy?: string | null;
                    },
                ) =>
                    exec((client) =>
                        client
                            .update(DBSchema.institutions)
                            .set({
                                guidId: params.guidId,
                                typeId: params.typeId,
                                modifiedBy: params.modifiedBy,
                                updatedAt: new Date().toISOString(),
                            })
                            .where(eq(DBSchema.institutions.id, params.id))
                            .returning({
                                id: DBSchema.institutions.id,
                                guidId: DBSchema.institutions.guidId,
                                typeId: DBSchema.institutions.typeId,
                                createdAt: DBSchema.institutions.createdAt,
                                updatedAt: DBSchema.institutions.updatedAt,
                                modifiedBy: DBSchema.institutions.modifiedBy,
                            }),
                    ),
            );

            const deleteOne = db.makeQuery((exec, id: string) =>
                exec((client) =>
                    client
                        .delete(DBSchema.institutions)
                        .where(eq(DBSchema.institutions.id, id))
                        .returning({ id: DBSchema.institutions.id }),
                ),
            );

            // Method to create establishment with translations
            const createWithTranslations = db.makeQuery(
                (
                    exec,
                    params: {
                        establishment: {
                            guidId?: string | null;
                            typeId: string;
                            modifiedBy?: string | null;
                        };
                        translations: Array<{
                            locale: string;
                            name?: string | null;
                            description?: string | null;
                            otherNames?: string | null;
                            acronyms?: string | null;
                        }>;
                    },
                ) =>
                    exec(async (client) => {
                        const [establishment] = await client
                            .insert(DBSchema.institutions)
                            .values({
                                guidId: params.establishment.guidId,
                                typeId: params.establishment.typeId,
                                modifiedBy: params.establishment.modifiedBy,
                            })
                            .returning({ id: DBSchema.institutions.id });

                        if (params.translations.length > 0) {
                            await client.insert(DBSchema.institutionsI18N).values(
                                params.translations.map((t) => ({
                                    dataId: establishment.id,
                                    locale: t.locale,
                                    name: t.name,
                                    description: t.description,
                                    otherNames: t.otherNames,
                                    acronyms: t.acronyms,
                                })),
                            );
                        }

                        // Return establishment with translations
                        return await client.query.institutions.findFirst({
                            where: eq(DBSchema.institutions.id, establishment.id),
                            columns: {
                                id: true,
                                guidId: true,
                                typeId: true,
                                createdAt: true,
                                updatedAt: true,
                                modifiedBy: true,
                            },
                            with: {
                                translations: {
                                    columns: {
                                        id: true,
                                        locale: true,
                                        name: true,
                                        description: true,
                                        otherNames: true,
                                        acronyms: true,
                                    },
                                },
                            },
                        });
                    }),
            );

            return {
                findAll,
                findById,
                createOne,
                updateOne,
                deleteOne,
                createWithTranslations,
            } as const;
        }),
    },
) { }