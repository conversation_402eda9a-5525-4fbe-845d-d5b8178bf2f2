import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InstitutionsRuntime } from '@/infrastructure/runtimes/institutions.runtime';
import { InstitutionsServiceLive } from '@/infrastructure/services/institutions.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateInstitutionSchema,
    InstitutionSchema,
    ResourceIdSchema,
    UpdateInstitutionSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import { Hono } from 'hono';

import * as Schema from 'effect/Schema';
// Import additional dependencies for OpenAPI
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllInstitutionsRoute = describeRoute({
    description: 'Get all Institutions',
    operationId: 'getAllInstitutions',
    responses: {
        200: {
            description: 'List of institutions',
            content: { 'application/json': { schema: resolver(Schema.Array(InstitutionSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Institutions'],
});

export const getInstitutionByIdRoute = describeRoute({
    description: 'Get an Institution by ID',
    operationId: 'getInstitutionById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Institution found',
            content: { 'application/json': { schema: resolver(InstitutionSchema) } },
        },
        404: {
            description: 'Institution not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Institutions'],
});

export const createInstitutionRoute = describeRoute({
    description: 'Create an Institution',
    operationId: 'createInstitution',
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(CreateInstitutionSchema) },
        },
    },
    responses: {
        201: {
            description: 'Institution created',
            content: { 'application/json': { schema: resolver(InstitutionSchema) } },
        },
        400: { description: 'Validation error' },
        500: { description: 'Internal server error' },
    },
    tags: ['Institutions'],
});

export const updateInstitutionRoute = describeRoute({
    description: 'Update an Institution',
    operationId: 'updateInstitution',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { schema: resolver(UpdateInstitutionSchema) },
        },
    },
    responses: {
        200: {
            description: 'Institution updated',
            content: { 'application/json': { schema: resolver(InstitutionSchema) } },
        },
        400: { description: 'Validation error' },
        404: { description: 'Institution not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Institutions'],
});

export const deleteInstitutionRoute = describeRoute({
    description: 'Delete an Institution',
    operationId: 'deleteInstitution',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
        },
    ],
    responses: {
        200: {
            description: 'Institution deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Institution not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Institutions'],
});

const institutionsRoute = new Hono();

institutionsRoute.get('/', getAllInstitutionsRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.getAllInstitutions();
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.get('/:id', getInstitutionByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.getInstitutionById(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.post('/', createInstitutionRoute, effectValidator('json', CreateInstitutionSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.createInstitution(body);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.put('/:id', updateInstitutionRoute, effectValidator('json', UpdateInstitutionSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.updateInstitution({ id, ...body });
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

institutionsRoute.delete('/:id', deleteInstitutionRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InstitutionsServiceLive;
        return yield* svc.deleteInstitution(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Institution deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { institutionsRoute };
