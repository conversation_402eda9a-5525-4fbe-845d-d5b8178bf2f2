{"name": "@rie/postgres-db", "version": "1.0.0", "type": "module", "license": "MIT", "description": "The postgres database package", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"import": "./build/index.js", "types": "./build/dts/index.d.ts"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "coverage": "vitest --coverage"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@rie/db-schema": "workspace:*", "@rie/biome-config": "workspace:*", "@rie/utils": "workspace:*", "pg": "^8.16.0"}, "devDependencies": {"@types/node": "^22.15.17", "@types/pg": "^8.15.1", "tsc-alias": "^1.8.16", "typescript": "^5.8.3"}, "peerDependencies": {"@effect/platform": "^0.82.0", "drizzle-orm": "^0.43.1", "effect": "^3.15.0"}}