import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class VisibilityMigrationConverter extends BaseConverter {
  private visibilityMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlVisibility: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.visibilityMappings.push({
      mysqlId: mysqlVisibility.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for visibility table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'visibilite',
      );

      if (insertStatements.length === 0) {
        console.log('No visibility INSERT statements found.');
        return;
      }

      let allPostgresRecords: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) =>
          this.convertToPostgres(record),
        );
        allPostgresRecords = [...allPostgresRecords, ...postgresRecords];
      }

      // Generate output with both inserts and mappings
      const postgresInserts = this.generatePostgresBaseTableInsertWithMappings(
        allPostgresRecords,
        'visibilities',
        'Visibility Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.visibilityMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'visibilite', postgres: 'visibilities' },
        idMappings,
      );

      console.log('Visibility migration completed successfully.');
    } catch (error) {
      console.error('Error during visibility migration:', error);
      throw error;
    }
  }
}
