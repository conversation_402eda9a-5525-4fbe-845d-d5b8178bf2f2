import * as Schema from 'effect/Schema';
import { descriptionSchema } from './base.schema';

// — Translation shape
export const UnitTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(descriptionSchema),
  otherNames: Schema.optional(Schema.String),
  acronyms: Schema.optional(Schema.String),
});

export type UnitTranslation = Schema.Schema.Type<typeof UnitTranslationSchema>;

// — Full Unit shape
export const UnitSchema = Schema.Struct({
  id: Schema.String,                             // cuid
  guidId: Schema.String,                         // required - references guids
  typeId: Schema.String,                         // required - references unit types
  parentId: Schema.optional(Schema.String),     // nullable - references unit parents
  translations: Schema.Array(UnitTranslationSchema), // at least []
  createdAt: Schema.String,                      // ISO timestamp
  updatedAt: Schema.String,                      // ISO timestamp
  modifiedBy: Schema.optional(Schema.String),   // nullable
});

export type Unit = Schema.Schema.Type<typeof UnitSchema>;

// — Input schemas for API
export const CreateUnitSchema = Schema.Struct({
  guidId: Schema.String,
  typeId: Schema.String,
  parentId: Schema.optional(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  ),
  modifiedBy: Schema.optional(Schema.String),
});

export type CreateUnitPayload = Schema.Schema.Type<typeof CreateUnitSchema>;

export const UpdateUnitSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.optional(Schema.String),
  parentId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.optional(Schema.String),
      description: Schema.optional(descriptionSchema),
      otherNames: Schema.optional(Schema.String),
      acronyms: Schema.optional(Schema.String),
    })
  )),
  modifiedBy: Schema.optional(Schema.String),
});

export type UpdateUnitPayload = Schema.Schema.Type<typeof UpdateUnitSchema> & { id: string };

// — Response schemas
export const UnitResponseSchema = UnitSchema;
export const UnitListResponseSchema = Schema.Array(UnitResponseSchema);

export type UnitResponse = Schema.Schema.Type<typeof UnitResponseSchema>;
export type UnitListResponse = Schema.Schema.Type<typeof UnitListResponseSchema>;
