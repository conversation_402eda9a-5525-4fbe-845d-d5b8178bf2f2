import { RoomsRepositoryLive } from '@/infrastructure/repositories/rooms.repository';
import {
    CreateRoomPayload,
    UpdateRoomPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class RoomsServiceLive extends Effect.Service<RoomsServiceLive>()(
    'RoomsServiceLive',
    {
        dependencies: [RoomsRepositoryLive],
        effect: Effect.gen(function* () {
            const roomsRepository = yield* RoomsRepositoryLive;

            const getAllRooms = () => {
                return Effect.gen(function* () {
                    return yield* roomsRepository.findAllRooms();
                });
            };

            const getRoomById = (id: string) => {
                return Effect.gen(function* () {
                    const room = yield* roomsRepository.findRoomById(id);
                    if (!room) {
                        return yield* Effect.fail(new Error(`Room with id ${id} not found`));
                    }
                    return room;
                });
            };

            const createRoom = (data: CreateRoomPayload) => {
                return Effect.gen(function* () {
                    return yield* roomsRepository.createRoom({
                        number: data.number,
                        area: data.area,
                        floorLoad: data.floorLoad,
                        buildingId: data.buildingId,
                        modifiedBy: data.modifiedBy,
                    });
                });
            };

            const updateRoom = (params: UpdateRoomPayload) => {
                const { id, ...updateData } = params;
                return Effect.gen(function* () {
                    const existingRoom = yield* roomsRepository.findRoomById(id);
                    if (!existingRoom) {
                        return yield* Effect.fail(new Error(`Room with id ${id} not found`));
                    }

                    return yield* roomsRepository.updateRoom({
                        id,
                        number: updateData.number,
                        area: updateData.area,
                        floorLoad: updateData.floorLoad,
                        buildingId: updateData.buildingId,
                        modifiedBy: updateData.modifiedBy,
                    });
                });
            };

            const deleteRoom = (id: string) => {
                return Effect.gen(function* () {
                    const existingRoom = yield* roomsRepository.findRoomById(id);
                    if (!existingRoom) {
                        return yield* Effect.fail(new Error(`Room with id ${id} not found`));
                    }
                    return yield* roomsRepository.deleteRoom(id);
                });
            };

            return {
                getAllRooms,
                getRoomById,
                createRoom,
                updateRoom,
                deleteRoom,
            } as const;
        }),
    },
) { }