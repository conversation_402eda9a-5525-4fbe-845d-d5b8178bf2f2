import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class PeopleRepositoryLive extends Effect.Service<PeopleRepositoryLive>()(
  'PeopleRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all
      const findAllPeople = db.makeQuery((exec) =>
        exec((client) =>
          client.query.people.findMany({
            columns: {
              id: true,
              guidId: true,
              uid: true,
              firstName: true,
              lastName: true,
              userId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
          }),
        ),
      );

      // — Fetch by ID
      const findPersonById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.people.findFirst({
            where: eq(DBSchema.people.id, id),
            columns: {
              id: true,
              guidId: true,
              uid: true,
              firstName: true,
              lastName: true,
              userId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
          }),
        ),
      );

      // — Create
      const createPerson = db.makeQuery(
        (
          exec,
          params: {
            guidId?: string | null;
            uid?: string | null;
            firstName: string;
            lastName: string;
            userId?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.people)
              .values({
                guidId: params.guidId,
                uid: params.uid,
                firstName: params.firstName,
                lastName: params.lastName,
                userId: params.userId,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })
              .returning({
                id: DBSchema.people.id,
                guidId: DBSchema.people.guidId,
                uid: DBSchema.people.uid,
                firstName: DBSchema.people.firstName,
                lastName: DBSchema.people.lastName,
                userId: DBSchema.people.userId,
                createdAt: DBSchema.people.createdAt,
                updatedAt: DBSchema.people.updatedAt,
                modifiedBy: DBSchema.people.modifiedBy,
              }),
          ),
      );

      // — Update
      const updatePerson = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            guidId?: string | null;
            uid?: string | null;
            firstName?: string;
            lastName?: string;
            userId?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.people)
              .set({
                guidId: params.guidId,
                uid: params.uid,
                firstName: params.firstName,
                lastName: params.lastName,
                userId: params.userId,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.people.id, params.id))
              .returning({
                id: DBSchema.people.id,
                guidId: DBSchema.people.guidId,
                uid: DBSchema.people.uid,
                firstName: DBSchema.people.firstName,
                lastName: DBSchema.people.lastName,
                userId: DBSchema.people.userId,
                createdAt: DBSchema.people.createdAt,
                updatedAt: DBSchema.people.updatedAt,
                modifiedBy: DBSchema.people.modifiedBy,
              }),
          ),
      );

      // — Delete
      const deletePerson = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.people)
            .where(eq(DBSchema.people.id, id))
            .returning({ id: DBSchema.people.id }),
        ),
      );

      return {
        findAllPeople,
        findPersonById,
        createPerson,
        updatePerson,
        deletePerson,
      } as const;
    }),
  },
) { }
