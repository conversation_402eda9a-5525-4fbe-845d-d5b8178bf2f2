import { addresses, campuses, equipments, locales, users } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import {
  boolean,
  index,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from 'drizzle-orm/pg-core';

export const serviceOffers = pgTable('service_offers', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  address: text().references(() => addresses.id),
  isForClinicalResearch: boolean().notNull().default(false),
  highlightService: boolean().notNull().default(false),
  createdBy: text().notNull(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const serviceOffersI18n = pgTable(
  'service_offers_i18n',
  {
    dataId: text()
      .references(() => serviceOffers.id, { onDelete: 'cascade' })
      .notNull(),
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    locale: text()
      .references(() => locales.code)
      .notNull(),
    name: text(),
    description: text(),
    serviceConditions: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.name),
      localeIdx: index('service_offers_i18n_locale_idx').on(table.locale),
    },
  ],
);

export const serviceOffersEquipments = pgTable(
  'service_offers_equipments',
  {
    serviceOfferId: text()
      .references(() => serviceOffers.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      })
      .notNull(),
    equipmentId: text()
      .references(() => equipments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      })
      .notNull(),
  },
  (table) => [
    {
      serviceOfferEquipmentPk: primaryKey({
        columns: [table.serviceOfferId, table.equipmentId],
      }),
      equipmentIdx: index('service_offer_equipments_equipment_idx').on(
        table.equipmentId,
      ),
    },
  ],
);

export const serviceOffersRelations = relations(
  serviceOffers,
  ({ many, one }) => ({
    translations: many(serviceOffersI18n),
    campus: one(campuses, {
      fields: [serviceOffers.address],
      references: [campuses.id],
    }),
  }),
);

export const serviceOffersI18nRelations = relations(
  serviceOffersI18n,
  ({ one }) => ({
    serviceOffer: one(serviceOffers, {
      fields: [serviceOffersI18n.dataId],
      references: [serviceOffers.id],
    }),
    locale: one(locales, {
      fields: [serviceOffersI18n.locale],
      references: [locales.code],
    }),
  }),
);

export const serviceOffersEquipmentsRelations = relations(
  serviceOffersEquipments,
  ({ one }) => ({
    serviceOffer: one(serviceOffers, {
      fields: [serviceOffersEquipments.serviceOfferId],
      references: [serviceOffers.id],
    }),
    equipment: one(equipments, {
      fields: [serviceOffersEquipments.equipmentId],
      references: [equipments.id],
    }),
  }),
);
