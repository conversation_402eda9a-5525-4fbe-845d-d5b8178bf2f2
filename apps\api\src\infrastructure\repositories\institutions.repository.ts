import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({
        url: env.PG_DATABASE_URL,
        ssl: env.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class InstitutionsRepositoryLive extends Effect.Service<InstitutionsRepositoryLive>()(
  'InstitutionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all
      const findAllInstitutions = db.makeQuery((exec) =>
        exec((client) =>
          client.query.institutions.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one by ID
      const findInstitutionById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.institutions.findFirst({
            where: eq(DBSchema.institutions.id, id),
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  acronyms: true,
                  otherNames: true,
                },
              },
            },
          }),
        ),
      );

      // — Create a bare institution
      const createInstitution = db.makeQuery(
        (
          exec,
          params: {
            guidId: string;
            typeId: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.institutions)
              .values({
                guidId: params.guidId,
                typeId: params.typeId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.institutions.id,
                guidId: DBSchema.institutions.guidId,
                typeId: DBSchema.institutions.typeId,
                createdAt: DBSchema.institutions.createdAt,
                updatedAt: DBSchema.institutions.updatedAt,
                modifiedBy: DBSchema.institutions.modifiedBy,
              }),
          ),
      );

      // — Update institution
      const updateInstitution = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            guidId?: string;
            typeId?: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.institutions)
              .set({
                guidId: params.guidId,
                typeId: params.typeId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.institutions.id, params.id))
              .returning({
                id: DBSchema.institutions.id,
                guidId: DBSchema.institutions.guidId,
                typeId: DBSchema.institutions.typeId,
                createdAt: DBSchema.institutions.createdAt,
                updatedAt: DBSchema.institutions.updatedAt,
                modifiedBy: DBSchema.institutions.modifiedBy,
              }),
          ),
      );

      // — Delete institution
      const deleteInstitution = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.institutions)
            .where(eq(DBSchema.institutions.id, id))
            .returning({ id: DBSchema.institutions.id }),
        ),
      );

      // — Create with translations
      const createInstitutionWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            institution: {
              guidId: string;
              typeId: string;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              acronyms?: string | null;
              otherNames?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            // 1) insert main row
            const [inst] = await client
              .insert(DBSchema.institutions)
              .values({
                guidId: params.institution.guidId,
                typeId: params.institution.typeId,
                modifiedBy: params.institution.modifiedBy,
              })
              .returning({ id: DBSchema.institutions.id });

            // 2) insert i18n rows
            if (params.translations.length > 0) {
              const rows = params.translations.map((t) => ({
                dataId: inst.id,
                locale: t.locale,
                name: t.name,
                description: t.description,
                acronyms: t.acronyms,
                otherNames: t.otherNames,
              }));
              await client.insert(DBSchema.institutionsI18N).values(rows);
            }

            return inst;
          }),
      );

      // — Update translations
      const updateInstitutionTranslations = db.makeQuery(
        (
          exec,
          params: {
            institutionId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              acronyms?: string | null;
              otherNames?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            // delete old
            await client
              .delete(DBSchema.institutionsI18N)
              .where(
                eq(DBSchema.institutionsI18N.dataId, params.institutionId),
              );

            // insert new
            if (params.translations.length > 0) {
              return await client
                .insert(DBSchema.institutionsI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.institutionId,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    acronyms: t.acronyms,
                    otherNames: t.otherNames,
                  })),
                )
                .returning({
                  id: DBSchema.institutionsI18N.id,
                  dataId: DBSchema.institutionsI18N.dataId,
                  locale: DBSchema.institutionsI18N.locale,
                  name: DBSchema.institutionsI18N.name,
                  description: DBSchema.institutionsI18N.description,
                  acronyms: DBSchema.institutionsI18N.acronyms,
                  otherNames: DBSchema.institutionsI18N.otherNames,
                });
            }

            return [];
          }),
      );

      return {
        findAllInstitutions,
        findInstitutionById,
        createInstitution,
        updateInstitution,
        deleteInstitution,
        createInstitutionWithTranslations,
        updateInstitutionTranslations,
      } as const;
    }),
  },
) {}
