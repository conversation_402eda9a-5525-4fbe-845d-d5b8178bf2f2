import {
  infrastructureAssociatedInnovationLabs,
  locales,
  users,
} from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const innovationLabs = pgTable('innovation_labs', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const innovationLabsRelations = relations(
  innovationLabs,
  ({ many }) => ({
    translations: many(innovationLabsI18N),
    associatedInfrastructures: many(infrastructureAssociatedInnovationLabs),
  }),
);

export const innovationLabsI18N = pgTable(
  'innovation_labs_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => innovationLabs.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const innovationLabsI18NRelations = relations(
  innovationLabsI18N,
  ({ one }) => ({
    innovationLabs: one(innovationLabs, {
      fields: [innovationLabsI18N.dataId],
      references: [innovationLabs.id],
    }),
    locale: one(locales, {
      fields: [innovationLabsI18N.locale],
      references: [locales.code],
    }),
  }),
);
