import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RolesRuntime } from '@/infrastructure/runtimes/roles.runtime';
import { RolesServiceLive } from '@/infrastructure/services/roles.service';
import { effectValidator } from '@hono/effect-validator';
import {
  ResourceIdSchema,
  RoleInputSchema,
  RoleSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Roles Routes
export const getAllRolesRoute = describeRoute({
  description: 'Obtient tous les rôles',
  operationId: 'getAllRoles',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(RoleSchema)),
        },
      },
      description: 'Rôles retournés avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const createRoleRoute = describeRoute({
  description: 'Crée un rôle',
  operationId: 'createRole',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoleInputSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(RoleSchema),
        },
      },
      description: 'Role créée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const updateRoleRoute = describeRoute({
  description: 'Mettre à jour un rôle',
  operationId: 'updateRole',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du role à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoleInputSchema),
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(RoleSchema),
        },
      },
      description: 'Role mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const deleteRoleRoute = describeRoute({
  description: 'Supprime un rôle',
  operationId: 'deleteRole',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle supprimée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

// Create the router
const rolesRoute = new Hono();

// Get all roles
rolesRoute.get('/', getAllRolesRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getAllRoles();
  });

  const roles = await RolesRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, roles);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(roles)) {
    return ctx.json(roles.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Get a role by ID
rolesRoute.get('/:id', async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getRoleById(id);
  });

  const maybeRole = await RolesRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, maybeRole);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(maybeRole)) {
    return ctx.json(maybeRole.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Create a new role
rolesRoute.post(
  '/',
  createRoleRoute,
  effectValidator('json', RoleInputSchema),
  async (ctx) => {
    const {
      name,
      description,
      directPermissionIds,
      parentRoleIds,
      permissionGroupIds,
    } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.createRole({
        name,
        description,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      });
    });

    const role = await RolesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, role);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(role)) {
      return ctx.json(role.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Update a role
rolesRoute.put(
  '/:id',
  updateRoleRoute,
  effectValidator('json', RoleInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.updateRole({ id, ...body });
    });

    const role = await RolesRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, role);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(role)) {
      return ctx.json(role.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Delete a role
rolesRoute.delete('/:id', deleteRoleRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.deleteRole(id);
  });

  const result = await RolesRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json({
      success: true,
      message: 'Role deleted successfully',
    });
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});
//
// // Add permissions to a role
// rolesRoute.post(
//   '/:id/permissions',
//   effectValidator('json', RolePermissionsSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { permissionIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.addPermissionsToRole(id, permissionIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (error.message === 'Role not found') {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );
//
// // Remove permissions from a role
// rolesRoute.delete(
//   '/:id/permissions',
//   effectValidator('json', RolePermissionsSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { permissionIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.removePermissionsFromRole(id, permissionIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (error.message === 'Role not found') {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );
//
// // Add parent roles to a role (inheritance)
// rolesRoute.post(
//   '/:id/inheritance',
//   effectValidator('json', RoleInheritanceSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { parentRoleIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.addParentRolesToRole(id, parentRoleIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (
//         error.message === 'Role not found' ||
//         error.message === 'Child role not found'
//       ) {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );
//
// // Remove parent roles from a role (inheritance)
// rolesRoute.delete(
//   '/:id/inheritance',
//   effectValidator('json', RoleInheritanceSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { parentRoleIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.removeParentRolesFromRole(id, parentRoleIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (
//         error.message === 'Role not found' ||
//         error.message === 'Child role not found'
//       ) {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );
//
// // Add permission groups to a role
// rolesRoute.post(
//   '/:id/permission-groups',
//   effectValidator('json', RolePermissionGroupsSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { groupIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.addPermissionGroupsToRole(id, groupIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (error.message === 'Role not found') {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );
//
// // Remove permission groups from a role
// rolesRoute.delete(
//   '/:id/permission-groups',
//   effectValidator('json', RolePermissionGroupsSchema),
//   async (ctx) => {
//     const id = ctx.req.param('id');
//     const { groupIds } = ctx.req.valid('json');
//
//     const program = Effect.gen(function* () {
//       const rolesService = yield* RolesServiceLive;
//       return yield* rolesService.removePermissionGroupsFromRole(id, groupIds);
//     });
//
//     try {
//       const result = await RolesRuntime.runPromise(program);
//       return ctx.json({ success: result });
//     } catch (error) {
//       if (error.message === 'Role not found') {
//         return ctx.json({ error: error.message }, 404);
//       }
//       return ctx.json({ error: error.message }, 400);
//     }
//   },
// );

// Get all permissions for a role
rolesRoute.get('/:id/permissions', async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getRolePermissions(id);
  });

  const permissions = await RolesRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, permissions);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(permissions)) {
    return ctx.json(permissions.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Get all permission groups for a role
rolesRoute.get('/:id/permission-groups', async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getRolePermissionGroups(id);
  });

  const permissionGroups = await RolesRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, permissionGroups);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(permissionGroups)) {
    return ctx.json(permissionGroups.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

export { rolesRoute };
