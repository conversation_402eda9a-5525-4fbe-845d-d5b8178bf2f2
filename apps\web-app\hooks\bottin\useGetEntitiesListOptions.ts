import type { RieServiceParams } from '@/constants/rie-client';
import type { DirectoryEntityKey } from '@/i18n/settings';
import { getEntitiesList } from '@/services/bottin/entities';
import { mapEntitiesToTableColumns } from '@/services/mappers/bottin/entities';
import type { DirectoryFull } from '@/types/bottin/directory';
import type { ApiReturnType } from '@/types/common';
import { queryOptions } from '@tanstack/react-query';

export function entitiesListOptions(
  pageParam: number,
  params: RieServiceParams,
  queryParams: string,
  directoryEntity: DirectoryEntityKey,
) {
  return queryOptions({
    queryFn: () =>
      getEntitiesList({
        directoryEntity,
        pageParam,
        params,
        queryParams,
      }),
    queryKey: [
      'directoryEntities',
      { directoryEntity, params, queryParams, pageParam },
    ],
    select: (data: ApiReturnType<DirectoryFull[]>) =>
      mapEntitiesToTableColumns(data, directoryEntity),
  });
}
