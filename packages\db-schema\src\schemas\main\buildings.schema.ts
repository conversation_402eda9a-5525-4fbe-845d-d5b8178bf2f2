import { users } from '@/schemas';
import { campuses, civicAddresses, locales, rooms } from '@/schemas';
import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { index, pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';

export const buildings = pgTable(
  'buildings',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    campusId: text().references(() => campuses.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    }),
    civicAddressId: text()
      // .notNull() // TODO: add back notNull() once we've added the address
      .references(() => civicAddresses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    sadId: text(),
    diId: text(),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      campusIdx: index().on(table.campusId),
    },
  ],
);

export const buildingsRelations = relations(buildings, ({ one, many }) => ({
  campus: one(campuses, {
    fields: [buildings.campusId],
    references: [campuses.id],
  }),
  civicAddress: one(civicAddresses, {
    fields: [buildings.civicAddressId],
    references: [civicAddresses.id],
  }),
  translations: many(buildingsI18N),
  rooms: many(rooms),
}));

export const buildingsI18N = pgTable(
  'buildings_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => buildings.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
    otherNames: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const buildingsI18NRelations = relations(buildingsI18N, ({ one }) => ({
  building: one(buildings, {
    fields: [buildingsI18N.dataId],
    references: [buildings.id],
  }),
  locale: one(locales, {
    fields: [buildingsI18N.locale],
    references: [locales.code],
  }),
}));
