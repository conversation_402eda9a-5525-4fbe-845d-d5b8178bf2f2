import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { DbUtils } from '@rie/utils';
import type {
  MySQLI18NDescription,
  PostgresI18NBaseReturn,
  PostgresI18NDescription,
} from './types';

export abstract class BaseConverter {
  protected generateCuid2(): string {
    return DbUtils.cuid2();
  }

  protected async loadEntityIdMappings(
    entityName: string,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  ): Promise<Record<string, any>> {
    try {
      return await this.getTableIdMappings(entityName);
    } catch (error) {
      console.error(`Error loading ${entityName} ID mappings:`, error);
      throw error;
    }
  }

  protected formatSqlValue(value: string | number | null | undefined): string {
    // console.log('values', value);

    if (value === undefined) {
      return 'NULL'; // Treat undefined as NULL
    }
    if (value === null) {
      return 'NULL';
    }
    if (typeof value === 'number') {
      return value.toString();
    }

    // Also replace any backslash-escaped quotes with properly escaped PostgreSQL quotes
    const fixedValue = value.replace(/\\'/g, "''");

    return `'${fixedValue}'`;
  }

  protected parseNullableString(value: string): string | null {
    if (value === 'NULL') {
      return null;
    }

    // Remove outer quotes if present
    let result = value.replace(/^'|'$/g, '');

    // Replace any backslash escapes with the actual character
    result = result.replace(/\\(.)/g, '$1');

    // Replace doubled single quotes with a single quote (SQL escaping)
    // result = result.replace(/''/g, "'");

    return result;
  }

  protected generateCommentHeader(description: string): string {
    return `-- ${description}\n-- Generated: ${new Date().toISOString()}\n\n`;
  }

  protected extractInsertStatements(
    sqlContent: string,
    tableName: string,
  ): string[] {
    // Clean up the SQL content first
    const cleanSqlContent = sqlContent
      .replace(/\/n/g, '\n') // Replace /n with actual newlines
      .trim();

    // Modified regex to handle the multi-line VALUES format
    const insertRegex = new RegExp(
      `INSERT\\s+INTO\\s+(?:\`|")?${tableName}(?:\`|")?\\s*\\(([^)]+)\\)\\s*VALUES\\s*([^;]+);`,
      'gim',
    );

    const matches = cleanSqlContent.match(insertRegex);

    if (!matches) {
      console.log('No matches found. Regex pattern:', insertRegex);
      return [];
    }

    const allInserts: string[] = [];

    for (const match of matches) {
      // Get the base INSERT part (everything up to VALUES)
      const baseInsert = match
        .substring(0, match.toUpperCase().indexOf('VALUES'))
        .trim();

      // Get the values part
      const valuesMatch = match.match(/VALUES\s*(.+?);$/is);
      if (!valuesMatch) {
        console.log('Warning: Could not extract values from statement');
        continue;
      }

      // Get just the values content and clean it up
      const valuesContent =
        valuesMatch[1]?.trim().replace(/^\(|\)$/g, '') ?? ''; // Remove outer parentheses

      // Split the values content into rows
      const valueRows = valuesContent
        .split(/\),\s*\(/g) // Split on '),(' with optional whitespace
        .map((row) => row.trim().replace(/^\(|\)$/g, '')) // Clean up each row
        .filter((row) => row.length > 0); // Remove empty rows

      // Create individual INSERT statements for each value set
      for (const row of valueRows) {
        const singleInsert = `${baseInsert} VALUES (${row});`;
        allInserts.push(singleInsert);
      }
    }

    return allInserts;
  }

  protected extractColumnNames(insertStatement: string): string[] {
    // Extract the column names between parentheses after the table name
    const columnMatch = insertStatement.match(
      /INSERT\s+INTO\s+`?\w+`?\s*\(([^)]+)\)/i,
    );
    if (!columnMatch || !columnMatch[1]) {
      throw new Error('Could not extract column names from INSERT statement');
    }

    // Split by comma and clean up each column name
    return columnMatch[1].split(',').map((col) => col.trim().replace(/`/g, ''));
  }

  // private splitValues(row: string): string[] {
  //   const values: string[] = [];
  //   let currentValue = '';
  //   let inQuotes = false;
  //   let quoteChar = '';

  //   for (let i = 0; i < row.length; i++) {
  //     const char = row[i];
  //     const nextChar = row[i + 1] || '';

  //     // Handle quotes
  //     if (char === "'" || char === '"') {
  //       if (!inQuotes) {
  //         // Starting a quoted section
  //         inQuotes = true;
  //         quoteChar = char;
  //         currentValue += char;
  //       } else if (char === quoteChar) {
  //         // Check for escaped quotes ('' in SQL)
  //         if (nextChar === quoteChar) {
  //           // This is an escaped quote, add it and skip the next one
  //           currentValue += char + nextChar;
  //           i++; // Skip the next quote
  //         } else {
  //           // This is a closing quote
  //           inQuotes = false;
  //           currentValue += char;
  //         }
  //       } else {
  //         // This is a different quote character inside quotes
  //         currentValue += char;
  //       }
  //     }
  //     // Only split on commas that are not inside quotes
  //     else if (char === ',' && !inQuotes) {
  //       values.push(currentValue.trim());
  //       currentValue = '';
  //     }
  //     // Add all other characters
  //     else {
  //       currentValue += char;
  //     }
  //   }

  //   // Add the last value if there is one
  //   if (currentValue) {
  //     values.push(currentValue.trim());
  //   }

  //   return values;
  // }

  protected extractValuesFromInsertStatement(
    insertStatement: string,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  ): Record<string, any> {
    // Get column names
    const columns = this.extractColumnNames(insertStatement);

    // Extract the values portion - this is a single row INSERT statement
    // created by our extractInsertStatements function
    const valuesMatch = insertStatement.match(/VALUES\s*\((.+?)\);$/is);
    if (!valuesMatch || !valuesMatch[1]) {
      throw new Error('Could not extract values from INSERT statement');
    }

    const valuesContent = valuesMatch[1];

    const valuesList = valuesContent.split(
      /,(?=(?:(?:[^'\\]|\\.|''|;)*)(?:'(?:[^'\\]|\\.|''|;)*'(?:[^'\\]|\\.|''|;)*)*$)/,
    );
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const values: Record<string, any> = {};

    // Check if we have the right number of values
    if (valuesList.length < columns.length) {
      console.warn(
        `Warning: Not enough values (${valuesList.length}) for columns (${columns.length})`,
      );
      console.warn(`Columns: ${columns.join(', ')}`);
      console.warn(`Values: ${valuesList.join(', ')}`);
    }

    columns.forEach((col, index) => {
      let value = index < valuesList.length ? valuesList[index]?.trim() : null;

      // Remove quotes if present
      if (value && (value.startsWith("'") || value.startsWith('"'))) {
        // Make sure we have a closing quote
        if (
          (value.startsWith("'") && value.endsWith("'")) ||
          (value.startsWith('"') && value.endsWith('"'))
        ) {
          // Remove the outer quotes
          value = value.slice(1, -1);

          // Replace any backslash escapes with the actual character
          // This handles cases like: 'Centre hospitalier de l\'Université de Montréal'
          // value = value.replace(/\\(.)/g, '$1');

          // // Replace doubled single quotes with a single quote (SQL escaping)
          // // This handles cases like: 'Centre hospitalier de l''Université de Montréal'
          // value = value.replace(/''/g, "'");
        } else {
          console.warn(
            `Warning: Value starts with quote but doesn't end with one: ${value}`,
          );
        }
      }

      // Convert 'NULL' string to null
      if (value === 'NULL') {
        value = null;
      }

      values[col] = value;
    });

    return values;
  }

  protected parseInsertValues(insertStatement: string): string[][] {
    // Extract the values portion of the INSERT statement
    const valuesMatch = insertStatement.match(/VALUES\s*\(([\s\S]*)\);$/is);
    if (!valuesMatch || !valuesMatch[1]) {
      console.log('No VALUES clause found in statement');
      return [];
    }

    // Split the values into individual rows
    const valuesContent = valuesMatch[1];

    // Split into rows and clean up
    const rows = valuesContent
      .split('),(')
      .map((row) => row.replace(/^\(|\)$/g, ''));

    console.log(`Found ${rows.length} rows`);

    // Parse each row into array of values
    return rows.map((row) => {
      // Split by comma but respect quoted strings
      const values: string[] = [];
      let currentValue = '';
      let inQuotes = false;

      for (let i = 0; i < row.length; i++) {
        const char = row[i];

        if (char === "'" && (i === 0 || row[i - 1] !== '\\')) {
          inQuotes = !inQuotes;
          currentValue += char;
        } else if (char === ',' && !inQuotes) {
          values.push(currentValue.trim());
          currentValue = '';
        } else {
          currentValue += char;
        }
      }

      // Add the last value
      values.push(currentValue.trim());

      return values;
    });
  }

  protected formatDate(dateStr: string | null): string | null {
    if (!dateStr || dateStr === 'NULL') {
      return null;
    }

    // Remove quotes if present
    const sanitizedDateStr = dateStr.replace(/^'|'$/g, '');

    try {
      // Parse the date string
      const date = new Date(sanitizedDateStr);
      if (Number.isNaN(date.getTime())) {
        console.warn('Invalid date:', sanitizedDateStr);
        return null;
      }

      // Format the date as ISO string
      return date.toISOString();
    } catch (error) {
      console.error('Error parsing date:', sanitizedDateStr, error);
      return null;
    }
  }

  protected async getTableIdMappings(
    tableName: string,
  ): Promise<Record<string, string>> {
    try {
      // Look for the mappings file in the output directory relative to the script directory
      const scriptDir = __dirname;
      const mappingsPath = path.join(scriptDir, 'output', 'id_mappings.json');
      const mappingsContent = await fs.readFile(mappingsPath, 'utf8');
      const allMappings = JSON.parse(mappingsContent);
      return allMappings[tableName] || {};
    } catch (error) {
      console.error(`Error reading mappings for ${tableName}:`, error);
      throw error;
    }
  }

  protected async writeMappingsToJson<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any> = Record<string, string>,
  >(
    outputDir: string,
    tables: { mysql: string; postgres: string },
    mappings: T,
  ): Promise<void> {
    const mappingsPath = path.join(outputDir, 'id_mappings.json');

    let allMappings = {};
    try {
      // Try to read existing mappings
      const existingContent = await fs.readFile(mappingsPath, 'utf8');
      allMappings = JSON.parse(existingContent);
    } catch (error) {
      // File doesn't exist or is invalid JSON, start with empty object
      console.log('Creating new mappings file');
    }

    // Update mappings for this table
    allMappings = { ...allMappings, [tables.mysql]: mappings };

    // Write back all mappings
    await fs.writeFile(mappingsPath, JSON.stringify(allMappings, null, 2));
  }

  protected generatePostgresBaseTableInsertWithMappings(
    entities: PostgresI18NBaseReturn[],
    tableName: string,
    header: string,
  ): string {
    let output = this.generateCommentHeader(header);

    // Add the actual INSERT statement
    const values = entities
      .map((entity) => `(${this.formatSqlValue(entity.id)})`)
      .join(',\n');

    output += `INSERT INTO "${tableName}" ("id") VALUES\n${values};\n\n`;

    return output;
  }

  protected generatePostgresI18NInsert(
    records: PostgresI18NDescription[],
    tableName: string,
    header: string,
  ): string {
    let output = this.generateCommentHeader(header);

    const values = records
      .map((record) => {
        const formattedValues =
          `(${this.formatSqlValue(record.id)}, ${this.formatSqlValue(record.data_id)}, ` +
          `${this.formatSqlValue(record.locale)}, ${this.formatSqlValue(record.name)}, ` +
          `${this.formatSqlValue(record.description)})`;
        return formattedValues;
      })
      .join(',\n');

    output += `INSERT INTO "${tableName}" ("id", "data_id", "locale", "name", "description") VALUES\n${values};\n\n`;
    return output;
  }

  protected generatePostgresWithColumnsI18NInsert<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any>,
  >(
    records: T[],
    tableName: string,
    header: string,
    columns: string[],
  ): string {
    let output = this.generateCommentHeader(header);

    const values = records
      .map((record) => {
        // Create a string array of values for each record
        const formattedValues = Object.entries(record)
          .map(([, value]) => {
            return this.formatSqlValue(value);
          })
          .join(', ');

        return `(${formattedValues})`;
        // return formattedValues;
      })
      .join(',\n');

    output += `INSERT INTO "${tableName}" (${columns.join(', ')}) VALUES\n${values};\n\n`;
    return output;
  }

  protected generateUpdatePostgresWithColumns<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any>,
  >(
    records: T[],
    tableName: string,
    header: string,
    columns: string[],
  ): string {
    let output = this.generateCommentHeader(header);

    const values = records
      .map((record) => {
        // Create a string array of values for each record
        const formattedValues = Object.entries(record)
          .map(([, value]) => {
            return this.formatSqlValue(value);
          })
          .join(', ');

        return `(${formattedValues})`;
        // return formattedValues;
      })
      .join(',\n');

    output += `UPDATE INTO "${tableName}" (${columns.join(', ')}) VALUES\n${values};\n\n`;
    return output;
  }

  protected parseI18NInsertStatement(
    sqlStatement: string,
  ): MySQLI18NDescription[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
        description: values.description,
      },
    ];
  }
}
