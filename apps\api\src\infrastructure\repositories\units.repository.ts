import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({
        url: env.PG_DATABASE_URL,
        ssl: env.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class UnitsRepositoryLive extends Effect.Service<UnitsRepositoryLive>()(
  'UnitsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      // — Fetch all units with translations
      const findAllUnits = db.makeQuery((exec) =>
        exec((client) =>
          client.query.units.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one unit by ID
      const findUnitById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.units.findFirst({
            where: eq(DBSchema.units.id, id),
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        ),
      );

      // — Create a unit
      const createUnit = db.makeQuery(
        (
          exec,
          params: {
            guidId: string;
            typeId: string;
            parentId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.units)
              .values({
                guidId: params.guidId,
                typeId: params.typeId,
                parentId: params.parentId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.units.id,
                guidId: DBSchema.units.guidId,
                typeId: DBSchema.units.typeId,
                parentId: DBSchema.units.parentId,
                createdAt: DBSchema.units.createdAt,
                updatedAt: DBSchema.units.updatedAt,
                modifiedBy: DBSchema.units.modifiedBy,
              }),
          ),
      );

      // — Update a unit
      const updateUnit = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            guidId?: string;
            typeId?: string;
            parentId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.units)
              .set({
                guidId: params.guidId,
                typeId: params.typeId,
                parentId: params.parentId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.units.id, params.id))
              .returning({
                id: DBSchema.units.id,
                guidId: DBSchema.units.guidId,
                typeId: DBSchema.units.typeId,
                parentId: DBSchema.units.parentId,
                createdAt: DBSchema.units.createdAt,
                updatedAt: DBSchema.units.updatedAt,
                modifiedBy: DBSchema.units.modifiedBy,
              }),
          ),
      );

      // — Delete a unit
      const deleteUnit = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.units)
            .where(eq(DBSchema.units.id, id))
            .returning({ id: DBSchema.units.id }),
        ),
      );

      // — Create a unit + translations
      const createUnitWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            unit: {
              guidId: string;
              typeId: string;
              parentId?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            const [u] = await client
              .insert(DBSchema.units)
              .values({
                guidId: params.unit.guidId,
                typeId: params.unit.typeId,
                parentId: params.unit.parentId,
                modifiedBy: params.unit.modifiedBy,
              })
              .returning({ id: DBSchema.units.id });

            if (params.translations.length > 0) {
              await client.insert(DBSchema.unitsI18N).values(
                params.translations.map((t) => ({
                  dataId: u.id,
                  locale: t.locale,
                  name: t.name,
                  description: t.description,
                  otherNames: t.otherNames,
                  acronyms: t.acronyms,
                })),
              );
            }

            return u;
          }),
      );

      // — Update unit translations
      const updateUnitTranslations = db.makeQuery(
        (
          exec,
          params: {
            unitId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            await client
              .delete(DBSchema.unitsI18N)
              .where(eq(DBSchema.unitsI18N.dataId, params.unitId));

            if (params.translations.length > 0) {
              return await client
                .insert(DBSchema.unitsI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.unitId,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    otherNames: t.otherNames,
                    acronyms: t.acronyms,
                  })),
                )
                .returning({
                  id: DBSchema.unitsI18N.id,
                  dataId: DBSchema.unitsI18N.dataId,
                  locale: DBSchema.unitsI18N.locale,
                  name: DBSchema.unitsI18N.name,
                  description: DBSchema.unitsI18N.description,
                  otherNames: DBSchema.unitsI18N.otherNames,
                  acronyms: DBSchema.unitsI18N.acronyms,
                });
            }
            return [];
          }),
      );

      return {
        findAllUnits,
        findUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
        createUnitWithTranslations,
        updateUnitTranslations,
      } as const;
    }),
  },
) { }
