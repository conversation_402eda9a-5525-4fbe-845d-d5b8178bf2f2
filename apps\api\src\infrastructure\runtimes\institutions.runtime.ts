import { ConfigLive } from '@/infrastructure/config/config.live';
import { InstitutionsRepositoryLive } from '@/infrastructure/repositories/institutions.repository';
import { InstitutionsServiceLive } from '@/infrastructure/services/institutions.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
    ConfigLive.pipe(
        Effect.map((env) =>
            PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
        ),
    ),
).pipe(Layer.provide(ConfigLive.Default));

const InstitutionsServicesLayer = Layer.mergeAll(
    InstitutionsRepositoryLive.Default,
    InstitutionsServiceLive.Default,
);

export const InstitutionsRuntime = ManagedRuntime.make(
    Layer.provide(
        InstitutionsServicesLayer,
        Layer.merge(ConfigLive.Default, PgDatabaseLive),
    ),
);