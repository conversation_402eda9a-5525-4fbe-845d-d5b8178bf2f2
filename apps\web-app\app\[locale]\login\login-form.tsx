'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { authClient } from '@/lib/better-auth';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useState, type ComponentProps, type FormEvent } from 'react';
import { redirect } from '@/lib/navigation';

export function LoginForm({ className, ...props }: ComponentProps<'div'>) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const locale = useAvailableLocale();

  const signIn = async (e: FormEvent) => {
    e.preventDefault();
    await authClient.signIn.email(
      {
        email,
        password,
      },
      {
        onRequest: () => {
          setLoading(true);
        },
        onSuccess: () => {
          redirect({ href: { pathname: '/' }, locale });
        },
        onError: (ctx) => {
          setLoading(false);
          alert(ctx.error.message);
        },
      },
    );
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle>Login to your account</CardTitle>
          <CardDescription>
            Enter your email below to login to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form>
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <a
                    // biome-ignore lint/a11y/useValidAnchor: <explanation>
                    href="#"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </a>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="flex flex-col gap-3">
                <Button className="w-full" disabled={loading} onClick={signIn}>
                  Sign in
                </Button>
                <Button
                  onClick={async () => {
                    try {
                      console.log('Initiating GitHub sign in...');
                      const result = await authClient.signIn.social({
                        provider: 'github',
                        callbackURL: `http://localhost:3000/rie/${locale}`,
                      });
                      console.log('Sign in result:', result);
                    } catch (error) {
                      console.error('GitHub sign in error:', error);
                    }
                  }}
                  variant="outline"
                  className="w-full"
                  type="button"
                >
                  Sign in with GitHub
                </Button>
              </div>
            </div>
            <div className="mt-4 text-center text-sm">
              Don&apos;t have an account?{' '}
              <Link href="/signup" className="underline underline-offset-4">
                Sign up
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
